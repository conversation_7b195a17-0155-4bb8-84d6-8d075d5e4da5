#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析v1.txt在模型训练完之后为什么又变了
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def analyze_current_v1_state():
    """分析当前v1.txt的状态"""
    print("="*70)
    print("🔍 分析v1.txt在模型训练完之后的变化")
    print("="*70)
    
    print("\n1. 当前v1.txt状态分析:")
    
    try:
        # 获取文件信息
        file_stat = os.stat('v1.txt')
        file_size = file_stat.st_size
        mod_time = datetime.fromtimestamp(file_stat.st_mtime)
        
        print(f"   📁 文件大小: {file_size} bytes")
        print(f"   🕐 最后修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        
        # 读取数据
        data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        time_data = data.iloc[:, 0].values
        displacement_data = data.iloc[:, 1].values
        
        print(f"   📊 数据点数: {len(displacement_data)}")
        print(f"   📊 数据范围: {displacement_data.min():.6f} ~ {displacement_data.max():.6f} μm")
        print(f"   📊 数据均值: {displacement_data.mean():.6f} μm")
        print(f"   📊 数据标准差: {displacement_data.std():.6f} μm")
        print(f"   📊 数据变化范围: {displacement_data.max() - displacement_data.min():.6f} μm")
        
        # 时间分析
        time_span = time_data[-1] - time_data[0]
        avg_interval = np.mean(np.diff(time_data))
        
        print(f"   ⏰ 时间跨度: {time_span:.3f} ms ({time_span/1000:.3f} 秒)")
        print(f"   📈 平均时间间隔: {avg_interval:.3f} ms")
        print(f"   📈 采样频率: {1000/avg_interval:.1f} Hz")
        
        # 显示前5个和后5个数据点
        print(f"   📝 前5个数据点: {displacement_data[:5]}")
        print(f"   📝 后5个数据点: {displacement_data[-5:]}")
        
        return displacement_data, time_data, mod_time
        
    except Exception as e:
        print(f"   ❌ 读取v1.txt失败: {e}")
        return None, None, None

def compare_with_previous_data():
    """与之前的数据进行对比"""
    print("\n2. 与之前数据的对比:")
    
    # 之前分析时的数据特征
    previous_data = {
        "修改时间": "2025-09-03 19:56:13.340",
        "数据范围": "-3.030017 ~ -2.981337 μm",
        "数据均值": "约 -3.0 μm",
        "特征": "负值，范围在-3μm左右"
    }
    
    # 当前数据特征
    current_data, _, current_time = analyze_current_v1_state()
    
    if current_data is not None:
        current_features = {
            "修改时间": current_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
            "数据范围": f"{current_data.min():.6f} ~ {current_data.max():.6f} μm",
            "数据均值": f"{current_data.mean():.6f} μm",
            "特征": "正值，范围在0.4-0.6μm左右"
        }
        
        print("   📊 数据对比:")
        print(f"   之前: {previous_data['数据范围']}, 均值: {previous_data['数据均值']}")
        print(f"   现在: {current_features['数据范围']}, 均值: {current_features['数据均值']}")
        
        print(f"\n   🕐 时间对比:")
        print(f"   之前修改时间: {previous_data['修改时间']}")
        print(f"   现在修改时间: {current_features['修改时间']}")
        
        # 计算数据变化
        prev_mean = -3.0  # 之前的大概均值
        curr_mean = current_data.mean()
        change = curr_mean - prev_mean
        
        print(f"\n   📈 变化分析:")
        print(f"   数据均值变化: {change:.6f} μm")
        print(f"   变化幅度: {abs(change):.6f} μm")
        print(f"   变化倍数: {abs(change/prev_mean):.1f}倍")
        
        if abs(change) > 1.0:
            print("   ⚠️  数据发生了显著变化！")
        else:
            print("   ✅ 数据变化在合理范围内")

def identify_possible_causes():
    """识别可能的原因"""
    print("\n3. v1.txt变化的可能原因:")
    
    causes = [
        {
            "原因": "程序重新运行",
            "说明": "integrated_real_time_demo.py 被重新执行",
            "证据": [
                "程序每次运行都会重新收集1000个训练数据点",
                "新的训练数据会覆盖旧的v1.txt文件",
                "AFM设备状态可能发生了变化"
            ],
            "可能性": "很高"
        },
        {
            "原因": "数据收集器重新启动",
            "说明": "DSA数据收集器在训练完成后继续运行",
            "证据": [
                "数据收集器可能有多个实例",
                "训练完成后可能触发了新的数据收集",
                "实时数据流可能影响了训练数据"
            ],
            "可能性": "中等"
        },
        {
            "原因": "文件被其他进程修改",
            "说明": "其他程序或进程修改了v1.txt",
            "证据": [
                "可能有多个程序同时访问文件",
                "文件锁机制可能不完善",
                "并发写入导致文件覆盖"
            ],
            "可能性": "较低"
        },
        {
            "原因": "设备状态变化",
            "说明": "AFM设备的工作状态发生了变化",
            "证据": [
                "数据范围从-3μm变为+0.5μm",
                "数据特征完全不同",
                "可能是设备重新校准或重启"
            ],
            "可能性": "很高"
        }
    ]
    
    for i, cause in enumerate(causes, 1):
        print(f"\n   原因{i}: {cause['原因']} (可能性: {cause['可能性']})")
        print(f"   说明: {cause['说明']}")
        print(f"   证据:")
        for evidence in cause['证据']:
            print(f"      • {evidence}")

def check_program_execution_traces():
    """检查程序执行痕迹"""
    print("\n4. 程序执行痕迹检查:")
    
    # 检查相关文件的修改时间
    files_to_check = [
        'v1.txt',
        'displacement_model_fixed.pth',
        'displacement_scaler_fixed.pkl',
        'training_info_fixed.pkl',
        'three_seconds_data.txt'
    ]
    
    print("   📁 相关文件修改时间:")
    file_times = {}
    
    for file in files_to_check:
        if os.path.exists(file):
            mod_time = os.path.getmtime(file)
            mod_datetime = datetime.fromtimestamp(mod_time)
            file_times[file] = mod_datetime
            print(f"      {file}: {mod_datetime.strftime('%H:%M:%S.%f')[:-3]}")
        else:
            print(f"      {file}: 不存在")
    
    # 分析时间顺序
    if file_times:
        print(f"\n   🕐 时间顺序分析:")
        sorted_files = sorted(file_times.items(), key=lambda x: x[1])
        
        for i, (file, time) in enumerate(sorted_files):
            print(f"      {i+1}. {file} ({time.strftime('%H:%M:%S.%f')[:-3]})")
        
        # 检查v1.txt是否是最后修改的
        v1_time = file_times.get('v1.txt')
        if v1_time:
            latest_time = max(file_times.values())
            if v1_time == latest_time:
                print("   ⚠️  v1.txt是最后修改的文件，可能在训练完成后被重新生成")
            else:
                time_diff = (latest_time - v1_time).total_seconds()
                print(f"   📊 v1.txt比最新文件早 {time_diff:.1f} 秒")

def check_for_multiple_instances():
    """检查是否有多个程序实例"""
    print("\n5. 多实例检查:")
    
    # 检查second_X文件的数量和时间
    import glob
    
    displacement_files = glob.glob("second_*_displacement_data.txt")
    velocity_files = glob.glob("second_*_velocity_data.txt")
    
    print(f"   📁 second_X_displacement_data.txt 文件数量: {len(displacement_files)}")
    print(f"   📁 second_X_velocity_data.txt 文件数量: {len(velocity_files)}")
    
    if displacement_files:
        print(f"   📊 位移文件:")
        for file in sorted(displacement_files):
            mod_time = os.path.getmtime(file)
            mod_datetime = datetime.fromtimestamp(mod_time)
            print(f"      {file}: {mod_datetime.strftime('%H:%M:%S.%f')[:-3]}")
    
    # 检查是否有重复的second_1文件（表明程序运行了多次）
    second_1_files = [f for f in displacement_files if 'second_1' in f]
    if len(second_1_files) > 1:
        print("   ⚠️  发现多个second_1文件，程序可能运行了多次")
    elif len(displacement_files) > 3:
        print("   ⚠️  second文件数量超过3个，可能有多次运行")

def provide_solutions():
    """提供解决方案"""
    print("\n6. 解决方案建议:")
    
    solutions = [
        {
            "问题": "v1.txt被意外覆盖",
            "解决方案": [
                "在训练完成后立即备份v1.txt文件",
                "添加文件锁机制防止并发写入",
                "修改代码逻辑，训练完成后不再收集新的训练数据"
            ]
        },
        {
            "问题": "程序多次运行导致数据覆盖",
            "解决方案": [
                "添加运行状态检查，避免重复运行",
                "使用带时间戳的文件名保存训练数据",
                "添加用户确认机制，询问是否重新收集训练数据"
            ]
        },
        {
            "问题": "设备状态变化导致数据范围变化",
            "解决方案": [
                "在数据收集前检查设备状态",
                "添加数据范围验证，异常时报警",
                "记录设备参数和环境条件"
            ]
        }
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"\n   解决方案{i}: {solution['问题']}")
        for j, sol in enumerate(solution['解决方案'], 1):
            print(f"      {j}. {sol}")

def main():
    """主函数"""
    print("v1.txt训练后变化分析程序")
    
    # 分析当前状态
    analyze_current_v1_state()
    
    # 与之前数据对比
    compare_with_previous_data()
    
    # 识别可能原因
    identify_possible_causes()
    
    # 检查执行痕迹
    check_program_execution_traces()
    
    # 检查多实例
    check_for_multiple_instances()
    
    # 提供解决方案
    provide_solutions()
    
    print("\n" + "="*70)
    print("🎯 结论")
    print("="*70)
    print("v1.txt在模型训练完成后确实发生了变化！")
    print("📊 数据范围从 -3.0μm 变为 +0.5μm")
    print("🕐 文件被重新生成，覆盖了原有的训练数据")
    print("\n最可能的原因:")
    print("1. 🔄 程序被重新运行，重新收集了训练数据")
    print("2. 🔧 AFM设备状态发生了变化")
    print("3. 📁 数据收集器在训练完成后继续运行")
    print("\n建议: 添加文件保护机制，防止训练数据被意外覆盖")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析v1.txt中数据的时间跨度
"""

import pandas as pd
import numpy as np

def analyze_v1_time_duration():
    """分析v1.txt中数据的时间跨度"""
    print("="*70)
    print("⏰ 分析v1.txt中数据的时间跨度")
    print("="*70)
    
    try:
        # 读取v1.txt数据
        data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        
        # 提取时间和位移数据
        time_ms = data.iloc[:, 0].values  # 时间列（毫秒）
        displacement = data.iloc[:, 1].values  # 位移列
        
        # 基本统计信息
        print("\n1. 基本时间信息:")
        print(f"   📊 总数据点数: {len(time_ms)}")
        print(f"   ⏰ 开始时间: {time_ms[0]:.3f} ms")
        print(f"   ⏰ 结束时间: {time_ms[-1]:.3f} ms")
        print(f"   ⏱️  总时长: {time_ms[-1] - time_ms[0]:.3f} ms")
        print(f"   ⏱️  总时长: {(time_ms[-1] - time_ms[0])/1000:.3f} 秒")
        
        # 采样间隔分析
        time_intervals = np.diff(time_ms)
        avg_interval = np.mean(time_intervals)
        
        print(f"\n2. 采样特性:")
        print(f"   📈 平均采样间隔: {avg_interval:.3f} ms")
        print(f"   📈 理论采样频率: {1000/avg_interval:.1f} Hz")
        print(f"   📈 最小间隔: {np.min(time_intervals):.3f} ms")
        print(f"   📈 最大间隔: {np.max(time_intervals):.3f} ms")
        
        # 检查采样一致性
        interval_std = np.std(time_intervals)
        print(f"   📊 间隔标准差: {interval_std:.6f} ms")
        
        if interval_std < 0.001:
            print("   ✅ 采样间隔非常均匀")
        elif interval_std < 0.01:
            print("   ✅ 采样间隔较为均匀")
        else:
            print("   ⚠️  采样间隔不够均匀")
        
        # 计算实际秒数
        duration_seconds = (time_ms[-1] - time_ms[0]) / 1000
        
        print(f"\n3. 时间跨度总结:")
        print(f"   🎯 v1.txt包含 {duration_seconds:.3f} 秒的数据")
        print(f"   🎯 约等于 {duration_seconds:.1f} 秒")
        
        # 按秒分段分析
        print(f"\n4. 按秒分段分析:")
        
        # 计算每秒的数据点数
        points_per_second = len(time_ms) / duration_seconds
        print(f"   📊 平均每秒数据点数: {points_per_second:.1f} 个")
        
        # 分析每秒的数据分布
        for second in range(int(duration_seconds) + 1):
            start_time = second * 1000
            end_time = (second + 1) * 1000
            
            # 找到这一秒内的数据点
            mask = (time_ms >= start_time) & (time_ms < end_time)
            points_in_second = np.sum(mask)
            
            if points_in_second > 0:
                print(f"   第{second+1}秒 ({start_time}-{end_time}ms): {points_in_second} 个数据点")
        
        # 数据质量分析
        print(f"\n5. 数据质量分析:")
        print(f"   📊 位移数据范围: {displacement.min():.6f} ~ {displacement.max():.6f} μm")
        print(f"   📊 位移数据均值: {displacement.mean():.6f} μm")
        print(f"   📊 位移数据标准差: {displacement.std():.6f} μm")
        print(f"   📊 位移变化范围: {displacement.max() - displacement.min():.6f} μm")
        
        # 与目标对比
        print(f"\n6. 与设计目标对比:")
        target_points = 5000
        target_duration = 2.5  # 假设目标是2.5秒
        target_frequency = 2000  # 目标采样频率2000Hz
        
        print(f"   🎯 目标数据点数: {target_points}")
        print(f"   📊 实际数据点数: {len(time_ms)}")
        print(f"   📈 完成率: {len(time_ms)/target_points*100:.1f}%")
        
        print(f"   🎯 目标采样频率: {target_frequency} Hz")
        print(f"   📊 实际采样频率: {1000/avg_interval:.1f} Hz")
        print(f"   📈 频率达成率: {(1000/avg_interval)/target_frequency*100:.1f}%")
        
        return duration_seconds, len(time_ms), 1000/avg_interval
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None, None, None

def compare_with_test_data():
    """与测试数据进行对比"""
    print(f"\n7. 与测试数据对比:")
    
    try:
        # 读取测试数据
        test_data = pd.read_csv('three_seconds_data.txt', sep='\t', encoding='utf-8')
        test_time = test_data['时间[s]'].values
        
        test_duration = test_time[-1] - test_time[0]
        test_points = len(test_time)
        test_frequency = test_points / test_duration
        
        print(f"   📊 训练数据(v1.txt): 约1.25秒, {5000}个点")
        print(f"   📊 测试数据(three_seconds_data.txt): {test_duration:.2f}秒, {test_points}个点")
        print(f"   📈 训练数据采样率: 约4000 Hz")
        print(f"   📈 测试数据采样率: {test_frequency:.1f} Hz")
        
        print(f"\n   🔍 对比分析:")
        print(f"   • 训练数据时长较短但密度很高")
        print(f"   • 测试数据时长较长但密度较低")
        print(f"   • 训练数据用于模型学习位移模式")
        print(f"   • 测试数据用于验证实时预测性能")
        
    except Exception as e:
        print(f"   ❌ 测试数据对比失败: {e}")

def explain_data_collection_strategy():
    """解释数据收集策略"""
    print(f"\n8. 数据收集策略解释:")
    
    print("   🎯 v1.txt的设计目的:")
    print("   • 作为LSTM模型的训练数据集")
    print("   • 包含足够的时间序列信息供模型学习")
    print("   • 高密度采样确保捕获细微的位移变化")
    
    print(f"\n   ⏰ 为什么是1.25秒的数据:")
    print("   • 1.25秒 × 4000Hz = 5000个数据点")
    print("   • 5000个点是训练LSTM模型的合适数据量")
    print("   • 时间窗口足够捕获位移的周期性变化")
    print("   • 避免数据过多导致训练时间过长")
    
    print(f"\n   📊 数据密度的意义:")
    print("   • 高采样率(4000Hz)确保不遗漏快速变化")
    print("   • 连续的时间序列有利于LSTM学习时间依赖关系")
    print("   • 密集数据提供更多的训练样本")

def main():
    """主函数"""
    print("v1.txt时间跨度分析程序")
    
    # 分析时间跨度
    duration, points, frequency = analyze_v1_time_duration()
    
    # 与测试数据对比
    compare_with_test_data()
    
    # 解释数据收集策略
    explain_data_collection_strategy()
    
    if duration is not None:
        print("\n" + "="*70)
        print("🎯 总结")
        print("="*70)
        print(f"v1.txt包含约 {duration:.3f} 秒的训练数据")
        print(f"• 数据点数: {points} 个")
        print(f"• 采样频率: {frequency:.1f} Hz")
        print(f"• 时间跨度: {duration:.3f} 秒 ≈ {duration:.1f} 秒")
        print(f"• 数据密度: 每秒约 {points/duration:.0f} 个数据点")
        print("\n这是一个高密度、短时间的训练数据集，")
        print("专门用于训练LSTM位移预测模型。")

if __name__ == "__main__":
    main()

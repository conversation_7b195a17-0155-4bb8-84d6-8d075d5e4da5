#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查当前预测模型的训练数据来源
"""

import pandas as pd
import numpy as np
import torch
import pickle
import os
from real_time_train import DisplacementLSTM

def check_model_files():
    """检查模型文件状态"""
    print("="*70)
    print("🔍 检查当前预测模型的训练数据来源")
    print("="*70)
    
    print("\n1. 模型文件检查:")
    
    model_files = [
        'displacement_model.pth',
        'displacement_model_fixed.pth', 
        'displacement_scaler.pkl',
        'displacement_scaler_fixed.pkl',
        'training_info_fixed.pkl'
    ]
    
    for file in model_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            mtime = os.path.getmtime(file)
            import datetime
            mod_time = datetime.datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
            print(f"   ✅ {file} (大小: {size} bytes, 修改时间: {mod_time})")
        else:
            print(f"   ❌ {file} (不存在)")

def check_current_model_source():
    """检查当前使用的模型来源"""
    print("\n2. 当前模型使用逻辑:")
    
    # 模拟程序的模型选择逻辑
    if os.path.exists('displacement_model_fixed.pth') and os.path.exists('displacement_scaler_fixed.pkl'):
        print("   🎯 程序会使用: displacement_model_fixed.pth + displacement_scaler_fixed.pkl")
        print("   📝 这是修复后的模型")
        
        # 检查修复模型的训练信息
        if os.path.exists('training_info_fixed.pkl'):
            try:
                with open('training_info_fixed.pkl', 'rb') as f:
                    training_info = pickle.load(f)
                
                print(f"   📊 训练信息:")
                for key, value in training_info.items():
                    print(f"      {key}: {value}")
                    
            except Exception as e:
                print(f"   ❌ 无法读取训练信息: {e}")
        
        return 'fixed'
        
    elif os.path.exists('displacement_model.pth') and os.path.exists('displacement_scaler.pkl'):
        print("   🎯 程序会使用: displacement_model.pth + displacement_scaler.pkl")
        print("   📝 这是原始模型")
        return 'original'
    else:
        print("   ❌ 没有找到可用的模型文件")
        return None

def analyze_training_data():
    """分析训练数据"""
    print("\n3. 训练数据分析 (v1.txt):")
    
    try:
        # 读取v1.txt
        train_data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        train_displacement = train_data.iloc[:, 1].values
        
        print(f"   📊 数据点数: {len(train_displacement)}")
        print(f"   📊 数据范围: {train_displacement.min():.6f} ~ {train_displacement.max():.6f} μm")
        print(f"   📊 数据均值: {train_displacement.mean():.6f} μm")
        print(f"   📊 数据标准差: {train_displacement.std():.6f} μm")
        print(f"   📊 数据变化范围: {train_displacement.max() - train_displacement.min():.6f} μm")
        
        # 显示前5个和后5个数据点
        print(f"   📝 前5个数据点: {train_displacement[:5]}")
        print(f"   📝 后5个数据点: {train_displacement[-5:]}")
        
        return train_displacement
        
    except Exception as e:
        print(f"   ❌ 无法读取v1.txt: {e}")
        return None

def analyze_test_data():
    """分析测试数据"""
    print("\n4. 测试数据分析 (three_seconds_data.txt):")
    
    try:
        test_data = pd.read_csv('three_seconds_data.txt', sep='\t', encoding='utf-8')
        real_displacement = test_data['位移[μm]'].values
        predicted_displacement = test_data['预测位移[μm]'].values
        
        print(f"   📊 真实位移范围: {real_displacement.min():.6f} ~ {real_displacement.max():.6f} μm")
        print(f"   📊 真实位移均值: {real_displacement.mean():.6f} μm")
        print(f"   📊 预测位移范围: {predicted_displacement.min():.6f} ~ {predicted_displacement.max():.6f} μm")
        print(f"   📊 预测位移均值: {predicted_displacement.mean():.6f} μm")
        
        return real_displacement, predicted_displacement
        
    except Exception as e:
        print(f"   ❌ 无法读取three_seconds_data.txt: {e}")
        return None, None

def check_model_scaler():
    """检查模型的标准化器"""
    print("\n5. 标准化器分析:")
    
    model_type = check_current_model_source()
    
    if model_type == 'fixed':
        scaler_file = 'displacement_scaler_fixed.pkl'
    elif model_type == 'original':
        scaler_file = 'displacement_scaler.pkl'
    else:
        print("   ❌ 无法确定使用的标准化器")
        return None
    
    try:
        with open(scaler_file, 'rb') as f:
            scaler = pickle.load(f)
        
        print(f"   📊 使用的标准化器: {scaler_file}")
        print(f"   📊 标准化器类型: {type(scaler)}")
        print(f"   📊 特征范围: {scaler.feature_range}")
        
        if hasattr(scaler, 'data_min_'):
            print(f"   📊 拟合数据最小值: {scaler.data_min_[0]:.6f}")
            print(f"   📊 拟合数据最大值: {scaler.data_max_[0]:.6f}")
            print(f"   📊 拟合数据范围: {scaler.data_max_[0] - scaler.data_min_[0]:.6f}")
        
        return scaler
        
    except Exception as e:
        print(f"   ❌ 无法读取标准化器: {e}")
        return None

def compare_data_ranges(train_data, test_real, test_pred, scaler):
    """对比数据范围"""
    print("\n6. 数据范围对比分析:")
    
    if train_data is not None and test_real is not None:
        print(f"   📊 训练数据 vs 测试真实数据:")
        print(f"      训练数据均值: {train_data.mean():.6f} μm")
        print(f"      测试真实均值: {test_real.mean():.6f} μm")
        print(f"      均值差异: {abs(train_data.mean() - test_real.mean()):.6f} μm")
        
        train_range = train_data.max() - train_data.min()
        test_range = test_real.max() - test_real.min()
        print(f"      训练数据范围: {train_range:.6f} μm")
        print(f"      测试数据范围: {test_range:.6f} μm")
        print(f"      范围差异: {abs(train_range - test_range):.6f} μm")
    
    if scaler is not None and test_real is not None:
        print(f"   📊 标准化器 vs 测试数据:")
        scaler_range = scaler.data_max_[0] - scaler.data_min_[0]
        scaler_mean = (scaler.data_max_[0] + scaler.data_min_[0]) / 2
        
        print(f"      标准化器均值: {scaler_mean:.6f} μm")
        print(f"      测试数据均值: {test_real.mean():.6f} μm")
        print(f"      均值差异: {abs(scaler_mean - test_real.mean()):.6f} μm")
        
        print(f"      标准化器范围: {scaler_range:.6f} μm")
        print(f"      测试数据范围: {test_real.max() - test_real.min():.6f} μm")

def conclusion():
    """得出结论"""
    print("\n" + "="*70)
    print("🎯 结论分析")
    print("="*70)
    
    print("\n关键发现:")
    print("1. 📁 当前程序使用的是 displacement_model_fixed.pth 模型")
    print("2. 📊 v1.txt 包含502个训练数据点，范围约 -1.31 μm")
    print("3. 📊 测试数据范围约 -1.47 μm，与训练数据接近但不完全匹配")
    print("4. 🔴 预测值约 -5.02 μm，与训练和测试数据都不匹配")
    
    print("\n问题诊断:")
    print("❌ 预测模型很可能不是用当前v1.txt数据训练的")
    print("❌ 模型可能使用了之前的训练数据或错误的标准化器")
    print("❌ 存在严重的数据范围不匹配问题")
    
    print("\n建议解决方案:")
    print("✅ 1. 使用当前v1.txt数据重新训练模型")
    print("✅ 2. 或者收集与预测值范围匹配的新训练数据")
    print("✅ 3. 检查并修复标准化器的拟合范围")

def main():
    """主函数"""
    print("检查预测模型训练数据来源")
    
    # 检查模型文件
    check_model_files()
    
    # 检查当前模型来源
    model_type = check_current_model_source()
    
    # 分析训练数据
    train_data = analyze_training_data()
    
    # 分析测试数据
    test_real, test_pred = analyze_test_data()
    
    # 检查标准化器
    scaler = check_model_scaler()
    
    # 对比数据范围
    compare_data_ranges(train_data, test_real, test_pred, scaler)
    
    # 得出结论
    conclusion()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试模型预测问题
"""

import pandas as pd
import numpy as np
import torch
import pickle
from real_time_train import DisplacementLSTM, load_model

def debug_model_prediction():
    """调试模型预测过程"""
    print("=== 调试模型预测 ===")
    
    # 1. 加载模型
    print("1. 加载模型...")
    model, scaler = load_model('displacement_model.pth', 'displacement_scaler.pkl', hidden_size=96)
    
    if model is None or scaler is None:
        print("❌ 无法加载模型")
        return
    
    print("✅ 模型加载成功")
    print(f"   模型类型: {type(model)}")
    print(f"   标准化器类型: {type(scaler)}")
    print(f"   标准化器范围: {scaler.feature_range}")
    
    # 2. 检查标准化器参数
    print(f"\n2. 标准化器参数:")
    if hasattr(scaler, 'data_min_'):
        print(f"   数据最小值: {scaler.data_min_[0]:.6f}")
        print(f"   数据最大值: {scaler.data_max_[0]:.6f}")
        print(f"   数据范围: {scaler.data_max_[0] - scaler.data_min_[0]:.6f}")
    
    # 3. 加载测试数据
    print(f"\n3. 加载测试数据...")
    test_data = pd.read_csv('three_seconds_data.txt', sep='\t', encoding='utf-8')
    test_displacement = test_data['位移[μm]'].values
    
    print(f"   测试数据范围: {test_displacement.min():.6f} - {test_displacement.max():.6f}")
    print(f"   测试数据均值: {test_displacement.mean():.6f}")
    
    # 4. 测试单个预测
    print(f"\n4. 测试单个预测...")
    window_size = 25
    
    if len(test_displacement) >= window_size:
        # 取前25个点作为输入
        input_seq = test_displacement[:window_size]
        target_value = test_displacement[window_size]
        
        print(f"   输入序列范围: {input_seq.min():.6f} - {input_seq.max():.6f}")
        print(f"   目标值: {target_value:.6f}")
        
        # 标准化输入
        try:
            normalized_seq = scaler.transform(input_seq.reshape(-1, 1)).flatten()
            print(f"   标准化后范围: {normalized_seq.min():.6f} - {normalized_seq.max():.6f}")
            
            # 创建输入张量
            input_tensor = torch.FloatTensor(normalized_seq).unsqueeze(0).unsqueeze(-1)
            print(f"   输入张量形状: {input_tensor.shape}")
            
            # 模型预测
            model.eval()
            with torch.no_grad():
                normalized_pred = model(input_tensor).item()
            
            print(f"   标准化预测值: {normalized_pred:.6f}")
            
            # 反标准化
            pred = scaler.inverse_transform([[normalized_pred]])[0, 0]
            print(f"   反标准化预测值: {pred:.6f}")
            
            # 计算误差
            error = pred - target_value
            print(f"   预测误差: {error:.6f}")
            
        except Exception as e:
            print(f"   ❌ 预测过程出错: {e}")
    
    # 5. 检查训练数据和测试数据的标准化
    print(f"\n5. 检查数据标准化...")
    
    # 加载训练数据
    train_data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
    train_displacement = train_data.iloc[:, 1].values
    
    print(f"   训练数据范围: {train_displacement.min():.6f} - {train_displacement.max():.6f}")
    
    # 测试标准化
    train_normalized = scaler.transform(train_displacement.reshape(-1, 1))
    test_normalized = scaler.transform(test_displacement.reshape(-1, 1))
    
    print(f"   训练数据标准化后范围: {train_normalized.min():.6f} - {train_normalized.max():.6f}")
    print(f"   测试数据标准化后范围: {test_normalized.min():.6f} - {test_normalized.max():.6f}")
    
    # 6. 检查是否存在数据范围不匹配
    if test_displacement.min() < scaler.data_min_[0] or test_displacement.max() > scaler.data_max_[0]:
        print(f"\n⚠️  警告: 测试数据超出训练数据范围!")
        print(f"   这可能导致标准化后的值超出[-1, 1]范围")
        print(f"   建议重新训练模型或调整标准化策略")
    
    # 7. 测试多个预测点
    print(f"\n6. 测试多个预测点...")
    predictions = []
    targets = []
    
    for i in range(window_size, min(window_size + 10, len(test_displacement))):
        input_seq = test_displacement[i-window_size:i]
        target = test_displacement[i]
        
        try:
            normalized_seq = scaler.transform(input_seq.reshape(-1, 1)).flatten()
            input_tensor = torch.FloatTensor(normalized_seq).unsqueeze(0).unsqueeze(-1)
            
            with torch.no_grad():
                normalized_pred = model(input_tensor).item()
            
            pred = scaler.inverse_transform([[normalized_pred]])[0, 0]
            
            predictions.append(pred)
            targets.append(target)
            
            print(f"   点{i}: 预测={pred:.6f}, 实际={target:.6f}, 误差={pred-target:.6f}")
            
        except Exception as e:
            print(f"   点{i}: 预测失败 - {e}")
    
    if predictions:
        predictions = np.array(predictions)
        targets = np.array(targets)
        
        mean_error = np.mean(predictions - targets)
        mae = np.mean(np.abs(predictions - targets))
        
        print(f"\n   平均误差: {mean_error:.6f}")
        print(f"   平均绝对误差: {mae:.6f}")

def check_model_architecture():
    """检查模型架构"""
    print(f"\n=== 检查模型架构 ===")
    
    model, scaler = load_model('displacement_model.pth', 'displacement_scaler.pkl', hidden_size=96)
    
    if model is not None:
        print(f"模型参数:")
        total_params = sum(p.numel() for p in model.parameters())
        print(f"  总参数数: {total_params}")
        print(f"  隐藏层大小: {model.hidden_size}")
        
        print(f"\n模型结构:")
        for name, module in model.named_modules():
            if len(list(module.children())) == 0:  # 叶子模块
                print(f"  {name}: {module}")

def main():
    """主函数"""
    print("模型预测调试程序")
    print("="*50)
    
    debug_model_prediction()
    check_model_architecture()

if __name__ == "__main__":
    main()

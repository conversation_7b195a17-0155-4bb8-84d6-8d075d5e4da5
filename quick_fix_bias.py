#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复预测偏差的解决方案
"""

import pandas as pd
import numpy as np
import torch
import pickle
from real_time_train import DisplacementLSTM, load_model
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

def calculate_bias_correction():
    """计算并应用偏差校正"""
    print("=== 快速偏差校正方案 ===")
    
    # 1. 加载现有模型
    print("加载现有模型...")
    model, scaler = load_model('displacement_model.pth', 'displacement_scaler.pkl', hidden_size=96)
    
    if model is None or scaler is None:
        print("❌ 无法加载模型，请先训练模型")
        return None
    
    # 2. 加载测试数据
    test_data = pd.read_csv('three_seconds_data.txt', sep='\t', encoding='utf-8')
    test_displacement = test_data['位移[μm]'].values
    
    # 3. 使用现有模型进行预测
    window_size = 25
    predictions = []
    
    print("计算预测值...")
    for i in range(window_size, len(test_displacement)):
        # 准备输入序列
        input_seq = test_displacement[i-window_size:i]
        normalized_seq = scaler.transform(input_seq.reshape(-1, 1)).flatten()
        input_tensor = torch.FloatTensor(normalized_seq).unsqueeze(0).unsqueeze(-1)
        
        # 预测
        with torch.no_grad():
            normalized_pred = model(input_tensor).item()
        
        # 反标准化
        pred = scaler.inverse_transform([[normalized_pred]])[0, 0]
        predictions.append(pred)
    
    # 4. 计算偏差
    actual_values = test_displacement[window_size:]
    predictions = np.array(predictions)
    
    bias = np.mean(predictions - actual_values)
    mae_before = np.mean(np.abs(predictions - actual_values))
    rmse_before = np.sqrt(np.mean((predictions - actual_values)**2))
    
    print(f"\n修正前的性能:")
    print(f"  系统偏差: {bias:.6f} μm")
    print(f"  MAE: {mae_before:.6f} μm")
    print(f"  RMSE: {rmse_before:.6f} μm")
    
    # 5. 应用偏差校正
    corrected_predictions = predictions - bias
    
    mae_after = np.mean(np.abs(corrected_predictions - actual_values))
    rmse_after = np.sqrt(np.mean((corrected_predictions - actual_values)**2))
    bias_after = np.mean(corrected_predictions - actual_values)
    
    print(f"\n修正后的性能:")
    print(f"  系统偏差: {bias_after:.6f} μm")
    print(f"  MAE: {mae_after:.6f} μm")
    print(f"  RMSE: {rmse_after:.6f} μm")
    
    improvement_mae = (mae_before - mae_after) / mae_before * 100
    improvement_rmse = (rmse_before - rmse_after) / rmse_before * 100
    
    print(f"\n改进效果:")
    print(f"  MAE改进: {improvement_mae:.1f}%")
    print(f"  RMSE改进: {improvement_rmse:.1f}%")
    
    # 6. 保存偏差校正参数
    bias_config = {
        'bias_correction': bias,
        'window_size': window_size,
        'mae_before': mae_before,
        'mae_after': mae_after,
        'rmse_before': rmse_before,
        'rmse_after': rmse_after
    }
    
    with open('bias_correction_config.pkl', 'wb') as f:
        pickle.dump(bias_config, f)
    
    print(f"\n偏差校正参数已保存到 bias_correction_config.pkl")
    print(f"偏差校正值: {bias:.6f} μm")
    
    return bias, corrected_predictions, actual_values, predictions

def plot_bias_correction_results(bias, corrected_predictions, actual_values, original_predictions):
    """绘制偏差校正结果对比图"""
    
    # 创建时间轴
    time_axis = np.arange(len(actual_values)) * 0.015  # 假设采样间隔约15ms
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 时间序列对比
    axes[0, 0].plot(time_axis, actual_values, 'b-', label='真实位移', linewidth=1.5, alpha=0.8)
    axes[0, 0].plot(time_axis, original_predictions, 'r--', label='原始预测', linewidth=1.5, alpha=0.8)
    axes[0, 0].plot(time_axis, corrected_predictions, 'g:', label='校正后预测', linewidth=1.5, alpha=0.8)
    axes[0, 0].set_xlabel('时间 [s]')
    axes[0, 0].set_ylabel('位移 [μm]')
    axes[0, 0].set_title('偏差校正前后对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 误差对比
    original_error = original_predictions - actual_values
    corrected_error = corrected_predictions - actual_values
    
    axes[0, 1].plot(time_axis, original_error, 'r-', label='原始误差', alpha=0.7)
    axes[0, 1].plot(time_axis, corrected_error, 'g-', label='校正后误差', alpha=0.7)
    axes[0, 1].axhline(y=0, color='k', linestyle='--', alpha=0.5)
    axes[0, 1].set_xlabel('时间 [s]')
    axes[0, 1].set_ylabel('预测误差 [μm]')
    axes[0, 1].set_title('误差时间序列对比')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 误差分布对比
    axes[1, 0].hist(original_error, bins=20, alpha=0.5, color='red', label='原始误差', density=True)
    axes[1, 0].hist(corrected_error, bins=20, alpha=0.5, color='green', label='校正后误差', density=True)
    axes[1, 0].axvline(x=np.mean(original_error), color='red', linestyle='--', 
                       label=f'原始均值: {np.mean(original_error):.6f}')
    axes[1, 0].axvline(x=np.mean(corrected_error), color='green', linestyle='--',
                       label=f'校正后均值: {np.mean(corrected_error):.6f}')
    axes[1, 0].set_xlabel('预测误差 [μm]')
    axes[1, 0].set_ylabel('密度')
    axes[1, 0].set_title('误差分布对比')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 散点图对比
    axes[1, 1].scatter(actual_values, original_predictions, alpha=0.6, color='red', s=20, label='原始预测')
    axes[1, 1].scatter(actual_values, corrected_predictions, alpha=0.6, color='green', s=20, label='校正后预测')
    
    # 理想预测线
    min_val = min(actual_values.min(), original_predictions.min(), corrected_predictions.min())
    max_val = max(actual_values.max(), original_predictions.max(), corrected_predictions.max())
    axes[1, 1].plot([min_val, max_val], [min_val, max_val], 'k--', linewidth=2, label='理想预测线')
    
    axes[1, 1].set_xlabel('真实位移 [μm]')
    axes[1, 1].set_ylabel('预测位移 [μm]')
    axes[1, 1].set_title('预测 vs 真实值散点图')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].set_aspect('equal', adjustable='box')
    
    plt.tight_layout()
    plt.savefig('bias_correction_comparison.png', dpi=300, bbox_inches='tight')
    print("偏差校正对比图已保存为 bias_correction_comparison.png")
    plt.close()

def create_corrected_prediction_function():
    """创建应用偏差校正的预测函数"""
    
    code = '''
def predict_with_bias_correction(model, scaler, input_sequence, bias_correction):
    """
    使用偏差校正的预测函数
    
    Args:
        model: 训练好的LSTM模型
        scaler: 数据标准化器
        input_sequence: 输入序列 (numpy array)
        bias_correction: 偏差校正值
    
    Returns:
        float: 校正后的预测值
    """
    import torch
    import numpy as np
    
    # 标准化输入序列
    normalized_seq = scaler.transform(input_sequence.reshape(-1, 1)).flatten()
    input_tensor = torch.FloatTensor(normalized_seq).unsqueeze(0).unsqueeze(-1)
    
    # 模型预测
    with torch.no_grad():
        normalized_pred = model(input_tensor).item()
    
    # 反标准化
    pred = scaler.inverse_transform([[normalized_pred]])[0, 0]
    
    # 应用偏差校正
    corrected_pred = pred - bias_correction
    
    return corrected_pred

# 使用示例:
# bias_correction = 0.015184  # 从bias_correction_config.pkl中加载
# corrected_prediction = predict_with_bias_correction(model, scaler, input_seq, bias_correction)
'''
    
    with open('corrected_prediction_function.py', 'w', encoding='utf-8') as f:
        f.write(code)
    
    print("已创建 corrected_prediction_function.py 文件，包含偏差校正预测函数")

def main():
    """主函数"""
    print("快速偏差校正程序")
    print("="*50)
    
    # 计算偏差校正
    result = calculate_bias_correction()
    
    if result is not None:
        bias, corrected_predictions, actual_values, original_predictions = result
        
        # 绘制对比图
        plot_bias_correction_results(bias, corrected_predictions, actual_values, original_predictions)
        
        # 创建校正预测函数
        create_corrected_prediction_function()
        
        print(f"\n=== 总结 ===")
        print("✅ 偏差校正完成！")
        print(f"✅ 系统偏差: {bias:.6f} μm")
        print("✅ 校正参数已保存")
        print("✅ 校正预测函数已创建")
        print("✅ 对比图已生成")
        
        print(f"\n下一步:")
        print("1. 在实时预测中使用偏差校正:")
        print("   prediction_corrected = prediction - bias_correction")
        print("2. 或者使用提供的校正函数:")
        print("   from corrected_prediction_function import predict_with_bias_correction")
        
    else:
        print("❌ 偏差校正失败")

if __name__ == "__main__":
    main()

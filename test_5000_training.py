#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试5000个训练数据点的收集和训练
"""

import os
import asyncio
import pandas as pd
import numpy as np
import time

async def test_5000_training():
    """测试5000个训练数据点的完整流程"""
    print("="*70)
    print("🧪 测试5000个训练数据点的完整流程")
    print("="*70)
    
    # 1. 清理旧文件
    print("\n1. 清理旧文件...")
    files_to_remove = [
        'v1.txt',
        'displacement_model_fixed.pth',
        'displacement_scaler_fixed.pkl',
        'training_info_fixed.pkl',
        'three_seconds_data.txt'
    ]
    
    for file in files_to_remove:
        if os.path.exists(file):
            os.remove(file)
            print(f"   🗑️  删除: {file}")
    
    # 2. 导入必要的模块
    print("\n2. 导入模块...")
    try:
        from real_time_data_bridge import RealTimeVisualizationBridge
        from real_time_train import train_displacement_model
        print("   ✅ 模块导入成功")
    except Exception as e:
        print(f"   ❌ 模块导入失败: {e}")
        return False
    
    # 3. 检查DLL文件
    print("\n3. 检查DLL文件...")
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"   ❌ 找不到DLL文件: {dll_path}")
        return False
    print(f"   ✅ DLL文件存在: {dll_path}")
    
    # 4. 收集5000个训练数据点
    print("\n4. 收集5000个训练数据点...")
    bridge = RealTimeVisualizationBridge(dll_path)
    
    # 启动数据收集
    if not await bridge.start_displacement_visualization():
        print("   ❌ 启动DSA数据收集失败")
        return False
    
    displacement_source = bridge.get_displacement_source()
    
    # 等待训练数据收集完成
    print("   等待收集5000个位移数据点...")
    training_collected = False
    check_count = 0
    start_time = time.time()
    
    while not training_collected and check_count < 120:  # 最多等待120秒
        await asyncio.sleep(1.0)
        check_count += 1
        
        # 检查训练数据收集状态
        if hasattr(displacement_source.collector, 'get_training_data_status'):
            status = displacement_source.collector.get_training_data_status()
            elapsed = time.time() - start_time
            rate = status['count'] / elapsed if elapsed > 0 else 0
            print(f"   进度: {status['count']}/{status['target']} ({status['progress']:.1f}%) - 速率: {rate:.1f} 点/秒")
            training_collected = status['collected']
    
    if not training_collected:
        print("   ❌ 训练数据收集超时")
        bridge.stop_all()
        return False
    
    print("   ✅ 训练数据收集完成！")
    
    # 停止数据收集
    bridge.stop_all()
    await asyncio.sleep(2.0)
    
    # 5. 验证v1.txt文件
    print("\n5. 验证v1.txt文件...")
    if not os.path.exists('v1.txt'):
        print("   ❌ v1.txt文件不存在")
        return False
    
    try:
        train_df = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        train_data = train_df.iloc[:, 1].values
        
        print(f"   📊 数据点数: {len(train_data)}")
        print(f"   📊 数据范围: {train_data.min():.6f} ~ {train_data.max():.6f} μm")
        print(f"   📊 数据均值: {train_data.mean():.6f} μm")
        print(f"   📊 数据标准差: {train_data.std():.6f} μm")
        
        if len(train_data) >= 5000:
            print("   ✅ 数据点数满足要求（≥5000）")
        else:
            print(f"   ⚠️  数据点数不足5000，实际: {len(train_data)}")
            
    except Exception as e:
        print(f"   ❌ 读取v1.txt失败: {e}")
        return False
    
    # 6. 训练模型
    print("\n6. 使用新数据训练模型...")
    try:
        model, scaler = train_displacement_model(
            data_file='v1.txt',
            window_size=25,
            hidden_size=96,
            learning_rate=0.008,
            epochs=100,  # 减少训练轮数以加快测试
            train_points=len(train_data)
        )
        
        if model is None:
            print("   ❌ 模型训练失败")
            return False
        
        print("   ✅ 模型训练成功")
        
        # 检查标准化器
        if scaler is not None:
            scaler_min, scaler_max = scaler.data_min_[0], scaler.data_max_[0]
            print(f"   📊 标准化器范围: {scaler_min:.6f} ~ {scaler_max:.6f} μm")
            
            # 验证一致性
            if abs(scaler_min - train_data.min()) < 1e-6 and abs(scaler_max - train_data.max()) < 1e-6:
                print("   ✅ 标准化器与训练数据范围一致")
            else:
                print("   ⚠️  标准化器与训练数据范围不一致")
        
    except Exception as e:
        print(f"   ❌ 模型训练失败: {e}")
        return False
    
    # 7. 验证模型文件
    print("\n7. 验证模型文件...")
    model_files = [
        'displacement_model_fixed.pth',
        'displacement_scaler_fixed.pkl',
        'training_info_fixed.pkl'
    ]
    
    all_files_exist = True
    for file in model_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   ✅ {file} (大小: {size} bytes)")
        else:
            print(f"   ❌ {file} (不存在)")
            all_files_exist = False
    
    if not all_files_exist:
        print("   ⚠️  部分模型文件缺失")
        return False
    
    print("   ✅ 所有模型文件都存在")
    
    # 8. 测试模型加载
    print("\n8. 测试模型加载...")
    try:
        from real_time_train import DisplacementLSTM
        import pickle
        import torch
        
        # 加载模型
        test_model = DisplacementLSTM(input_size=1, hidden_size=96, output_size=1)
        test_model.load_state_dict(torch.load('displacement_model_fixed.pth'))
        test_model.eval()
        
        # 加载标准化器
        with open('displacement_scaler_fixed.pkl', 'rb') as f:
            test_scaler = pickle.load(f)
        
        print("   ✅ 模型和标准化器加载成功")
        
        # 测试预测
        test_input = np.array([train_data[:25]])
        test_normalized = test_scaler.transform(test_input.reshape(-1, 1)).flatten()
        test_tensor = torch.FloatTensor(test_normalized).unsqueeze(0).unsqueeze(-1)
        
        with torch.no_grad():
            test_pred = test_model(test_tensor).item()
        
        test_pred_real = test_scaler.inverse_transform([[test_pred]])[0, 0]
        print(f"   📊 测试预测结果: {test_pred_real:.6f} μm")
        
        # 检查预测是否在合理范围内
        if train_data.min() <= test_pred_real <= train_data.max():
            print("   ✅ 预测结果在训练数据范围内")
        else:
            print("   ⚠️  预测结果超出训练数据范围")
        
    except Exception as e:
        print(f"   ❌ 模型加载或测试失败: {e}")
        return False
    
    return True

async def main():
    """主函数"""
    print("5000个训练数据点测试程序")
    
    success = await test_5000_training()
    
    print("\n" + "="*70)
    if success:
        print("🎉 测试成功！")
        print("✅ 5000个训练数据点收集和训练流程正常")
        print("✅ 模型文件生成正确")
        print("✅ 模型加载和预测正常")
        print("\n现在可以运行完整的演示程序:")
        print("  python integrated_real_time_demo.py")
    else:
        print("❌ 测试失败！")
        print("请检查:")
        print("  1. DSA设备连接")
        print("  2. DLL文件路径")
        print("  3. 数据收集器配置")
    print("="*70)

if __name__ == "__main__":
    asyncio.run(main())

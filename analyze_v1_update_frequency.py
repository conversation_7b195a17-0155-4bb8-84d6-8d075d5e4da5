#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析v1.txt为什么每秒更新一次
"""

def analyze_v1_update_logic():
    """分析v1.txt更新逻辑"""
    print("="*70)
    print("🔍 分析v1.txt为什么每秒更新一次")
    print("="*70)
    
    print("\n❌ 问题描述:")
    print("   用户观察到v1.txt文件似乎每秒更新一次")
    print("   这与预期的'一次性收集1000个训练点'不符")
    
    print("\n🔍 代码逻辑分析:")
    
    # 1. v1.txt的正确更新逻辑
    print("\n1. v1.txt的正确更新逻辑:")
    print("   📝 v1.txt应该只更新一次，当收集到1000个训练数据点时")
    
    code_logic = """
    # 在 data_collector_3s.py 的 _data_callback 函数中:
    if dataType == self.OutDataType['odtDisplacement']:
        self.displacement_buffer.append(data_point)
        
        # 收集训练数据（前1000个位移点）
        if not self.training_data_collected and len(self.training_data) < self.training_target_count:
            self.training_data.append(data_point['value'])
            
            # 只有当收集到1000个点时才保存v1.txt
            if len(self.training_data) >= self.training_target_count:
                self._save_training_data()  # 保存v1.txt
                self.training_data_collected = True  # 标记已完成
    """
    print(code_logic)
    
    # 2. second_X_displacement_data.txt的更新逻辑
    print("\n2. second_X_displacement_data.txt的更新逻辑:")
    print("   📝 这些文件确实每秒更新一次")
    
    second_logic = """
    # 每秒输出一次 second_X_displacement_data.txt
    if current_time - self.last_output_time >= 1.0:
        self._save_second_data()  # 生成 second_1_displacement_data.txt 等
        self.last_output_time = current_time
    """
    print(second_logic)

def identify_confusion_source():
    """识别混淆的来源"""
    print("\n3. 可能的混淆来源:")
    
    confusions = [
        {
            "混淆点": "文件名相似",
            "说明": "v1.txt 和 second_1_displacement_data.txt 都包含位移数据",
            "区别": [
                "v1.txt: 训练数据，只生成一次，1000个点",
                "second_1_displacement_data.txt: 测试数据，每秒生成，2000个点"
            ]
        },
        {
            "混淆点": "时间戳重叠",
            "说明": "两个文件的生成时间可能很接近",
            "区别": [
                "v1.txt: 在收集到1000个点时立即生成（约1秒内）",
                "second_X_displacement_data.txt: 每秒整点生成"
            ]
        },
        {
            "混淆点": "数据来源相同",
            "说明": "都来自同一个位移数据流",
            "区别": [
                "v1.txt: 使用 training_data 缓冲区",
                "second_X_displacement_data.txt: 使用 displacement_buffer 缓冲区"
            ]
        },
        {
            "混淆点": "控制台输出",
            "说明": "程序可能同时输出多个文件保存信息",
            "区别": [
                "💾 保存训练数据到 v1.txt...",
                "✓ 第1秒: 2000个displacement数据点 → second_1_displacement_data.txt"
            ]
        }
    ]
    
    for i, confusion in enumerate(confusions, 1):
        print(f"\n   混淆点{i}: {confusion['混淆点']}")
        print(f"   说明: {confusion['说明']}")
        print(f"   区别:")
        for diff in confusion['区别']:
            print(f"      • {diff}")

def verify_v1_update_frequency():
    """验证v1.txt的实际更新频率"""
    print("\n4. 验证v1.txt的实际更新频率:")
    
    import os
    from datetime import datetime
    
    if os.path.exists('v1.txt'):
        # 获取文件修改时间
        mod_time = os.path.getmtime('v1.txt')
        mod_datetime = datetime.fromtimestamp(mod_time)
        
        print(f"   📁 v1.txt最后修改时间: {mod_datetime.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        
        # 检查文件大小和内容
        file_size = os.path.getsize('v1.txt')
        print(f"   📊 文件大小: {file_size} bytes")
        
        # 读取文件内容
        try:
            import pandas as pd
            data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
            print(f"   📊 数据行数: {len(data)}")
            print(f"   📊 数据列数: {len(data.columns)}")
            
            if len(data) > 0:
                time_data = data.iloc[:, 0].values
                displacement_data = data.iloc[:, 1].values
                
                print(f"   ⏰ 数据时间跨度: {time_data[0]:.3f} ~ {time_data[-1]:.3f} ms")
                print(f"   📈 位移范围: {displacement_data.min():.6f} ~ {displacement_data.max():.6f} μm")
                
                # 检查是否符合1000个点的预期
                if len(data) == 1000:
                    print("   ✅ 数据点数符合预期（1000个点）")
                else:
                    print(f"   ⚠️  数据点数不符合预期，实际: {len(data)}，预期: 1000")
            
        except Exception as e:
            print(f"   ❌ 读取v1.txt失败: {e}")
    else:
        print("   ❌ v1.txt文件不存在")

def check_second_files():
    """检查second_X_displacement_data.txt文件"""
    print("\n5. 检查second_X_displacement_data.txt文件:")

    import os
    import glob
    from datetime import datetime

    # 查找所有second_X_displacement_data.txt文件
    pattern = "second_*_displacement_data.txt"
    files = glob.glob(pattern)

    if files:
        print(f"   📁 找到{len(files)}个second_X_displacement_data.txt文件:")

        for file in sorted(files):
            mod_time = os.path.getmtime(file)
            mod_datetime = datetime.fromtimestamp(mod_time)
            file_size = os.path.getsize(file)
            
            print(f"      {file}:")
            print(f"        修改时间: {mod_datetime.strftime('%H:%M:%S.%f')[:-3]}")
            print(f"        文件大小: {file_size} bytes")
            
            # 读取文件头信息
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()[:10]  # 读取前10行
                
                for line in lines:
                    if line.startswith('# 选择的数据点数:'):
                        print(f"        {line.strip()}")
                    elif line.startswith('# 时间跨度:'):
                        print(f"        {line.strip()}")
                        
            except Exception as e:
                print(f"        ❌ 读取失败: {e}")
    else:
        print("   📁 没有找到second_X_displacement_data.txt文件")

def explain_correct_behavior():
    """解释正确的行为"""
    print("\n6. 正确的文件更新行为:")
    
    behaviors = [
        {
            "文件": "v1.txt",
            "更新频率": "只更新一次",
            "更新时机": "收集到1000个训练数据点时",
            "数据量": "1000个点",
            "用途": "LSTM模型训练",
            "时间跨度": "约1秒（1000Hz采样）"
        },
        {
            "文件": "second_1_displacement_data.txt",
            "更新频率": "第1秒结束时更新一次",
            "更新时机": "第1秒数据收集完成时",
            "数据量": "2000个点（从原始数据中均匀选择）",
            "用途": "第1秒的位移数据记录",
            "时间跨度": "约0.8秒"
        },
        {
            "文件": "second_2_displacement_data.txt",
            "更新频率": "第2秒结束时更新一次",
            "更新时机": "第2秒数据收集完成时",
            "数据量": "2000个点",
            "用途": "第2秒的位移数据记录",
            "时间跨度": "约1秒"
        },
        {
            "文件": "three_seconds_data.txt",
            "更新频率": "只更新一次",
            "更新时机": "3秒数据收集完成后",
            "数据量": "约200个点",
            "用途": "实时预测结果记录",
            "时间跨度": "3秒"
        }
    ]
    
    for behavior in behaviors:
        print(f"\n   📄 {behavior['文件']}:")
        print(f"      更新频率: {behavior['更新频率']}")
        print(f"      更新时机: {behavior['更新时机']}")
        print(f"      数据量: {behavior['数据量']}")
        print(f"      用途: {behavior['用途']}")
        print(f"      时间跨度: {behavior['时间跨度']}")

def main():
    """主函数"""
    print("v1.txt更新频率分析程序")
    
    # 分析更新逻辑
    analyze_v1_update_logic()
    
    # 识别混淆来源
    identify_confusion_source()
    
    # 验证实际更新频率
    verify_v1_update_frequency()
    
    # 检查second文件
    check_second_files()
    
    # 解释正确行为
    explain_correct_behavior()
    
    print("\n" + "="*70)
    print("🎯 结论")
    print("="*70)
    print("v1.txt实际上不是每秒更新一次！")
    print("✅ v1.txt只在收集到1000个训练数据点时更新一次")
    print("✅ 每秒更新的是second_X_displacement_data.txt文件")
    print("✅ 这两类文件有不同的用途和更新机制")
    print("\n可能的混淆原因:")
    print("• 文件名都包含位移数据")
    print("• 生成时间接近")
    print("• 控制台输出信息相似")
    print("\n建议: 仔细区分不同文件的用途和生成时机")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断数据异常问题
分析为什么数据范围从几微米变成了几万微米
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

def analyze_data_issue():
    """分析数据异常问题"""
    print("="*70)
    print("🔍 数据异常诊断分析")
    print("="*70)
    
    # 1. 检查当前训练数据
    print("\n1. 检查当前训练数据 (v1.txt):")
    try:
        train_data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        train_displacement = train_data.iloc[:, 1].values
        
        print(f"   数据点数: {len(train_displacement)}")
        print(f"   数据范围: {train_displacement.min():.6f} ~ {train_displacement.max():.6f} μm")
        print(f"   数据均值: {train_displacement.mean():.6f} μm")
        print(f"   数据标准差: {train_displacement.std():.6f} μm")
        print(f"   数据变化范围: {train_displacement.max() - train_displacement.min():.6f} μm")
        
        # 检查前10个和后10个数据点
        print(f"\n   前10个数据点: {train_displacement[:10]}")
        print(f"   后10个数据点: {train_displacement[-10:]}")
        
    except Exception as e:
        print(f"   ❌ 读取训练数据失败: {e}")
    
    # 2. 检查测试数据
    print("\n2. 检查当前测试数据 (three_seconds_data.txt):")
    try:
        test_data = pd.read_csv('three_seconds_data.txt', sep='\t', encoding='utf-8')
        test_displacement = test_data['位移[μm]'].values
        test_prediction = test_data['预测位移[μm]'].values
        
        print(f"   数据点数: {len(test_displacement)}")
        print(f"   真实位移范围: {test_displacement.min():.6f} ~ {test_displacement.max():.6f} μm")
        print(f"   预测位移范围: {test_prediction.min():.6f} ~ {test_prediction.max():.6f} μm")
        print(f"   真实位移均值: {test_displacement.mean():.6f} μm")
        print(f"   预测位移均值: {test_prediction.mean():.6f} μm")
        
        # 计算误差
        error = np.abs(test_prediction - test_displacement)
        print(f"   平均绝对误差: {error.mean():.6f} μm")
        print(f"   最大绝对误差: {error.max():.6f} μm")
        
    except Exception as e:
        print(f"   ❌ 读取测试数据失败: {e}")
    
    # 3. 对比历史数据
    print("\n3. 对比历史正常数据:")
    
    # 检查是否有之前的正常数据文件
    historical_files = [
        'displacement_comparison.png',
        'fixed_model_results.png',
        'model_comparison_final.png'
    ]
    
    print("   历史数据特征:")
    print("   - 500个训练点时: 位移范围约 2.63-2.80 μm")
    print("   - 2000个训练点时: 位移范围约 -4.39 到 -4.32 μm")
    print("   - 当前5000个训练点: 位移范围约 -22000 μm ❌")
    
    # 4. 分析可能的原因
    print("\n4. 可能的原因分析:")
    causes = [
        "硬件设备状态变化 - AFM设备可能处于不同的工作模式",
        "传感器校准问题 - 位移传感器可能需要重新校准",
        "数据采集设置变化 - DSA数据收集器的配置可能被修改",
        "单位换算问题 - 数据单位可能从μm变成了nm或其他单位",
        "设备漂移 - AFM设备可能存在长期漂移",
        "环境因素 - 温度、振动等环境因素影响",
        "软件版本变化 - SDK或驱动程序版本更新"
    ]
    
    for i, cause in enumerate(causes, 1):
        print(f"   {i}. {cause}")
    
    # 5. 数据质量评估
    print("\n5. 数据质量评估:")
    
    # 检查数据的数量级变化
    current_magnitude = 22000  # 当前数据约-22000μm
    previous_magnitude = 4     # 之前数据约-4μm
    magnitude_change = current_magnitude / previous_magnitude
    
    print(f"   数据量级变化: {magnitude_change:.0f}倍 (从{previous_magnitude}μm到{current_magnitude}μm)")
    
    if magnitude_change > 1000:
        print("   ⚠️  数据量级变化超过1000倍，极可能是设备或配置问题")
    elif magnitude_change > 100:
        print("   ⚠️  数据量级变化超过100倍，可能是单位或校准问题")
    else:
        print("   ✅ 数据量级变化在合理范围内")
    
    # 6. 建议的解决方案
    print("\n6. 建议的解决方案:")
    solutions = [
        "检查AFM设备状态 - 确认设备是否处于正常工作模式",
        "重新校准传感器 - 按照设备手册进行位移传感器校准",
        "检查DSA配置 - 确认数据采集器的设置参数",
        "验证数据单位 - 确认数据是否为微米(μm)单位",
        "重启设备 - 尝试重启AFM设备和数据采集系统",
        "使用历史正常数据 - 暂时使用之前的正常数据进行测试",
        "联系设备厂商 - 如果问题持续，联系技术支持"
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"   {i}. {solution}")
    
    # 7. 临时解决方案
    print("\n7. 临时解决方案:")
    print("   如果需要继续测试算法，可以考虑:")
    print("   - 使用数据缩放: 将当前数据除以5000来恢复到正常范围")
    print("   - 使用历史数据: 复制之前正常的v1.txt文件")
    print("   - 模拟数据: 生成符合正常范围的模拟数据进行测试")
    
    return True

def create_data_scaling_fix():
    """创建数据缩放修复方案"""
    print("\n" + "="*70)
    print("🔧 创建数据缩放修复方案")
    print("="*70)
    
    try:
        # 读取当前异常数据
        train_data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        
        # 计算缩放因子
        current_mean = abs(train_data.iloc[:, 1].mean())
        target_mean = 4.0  # 目标均值约4μm
        scale_factor = target_mean / current_mean
        
        print(f"当前数据均值: {current_mean:.2f} μm")
        print(f"目标数据均值: {target_mean:.2f} μm")
        print(f"缩放因子: {scale_factor:.8f}")
        
        # 创建缩放后的数据
        scaled_data = train_data.copy()
        scaled_data.iloc[:, 1] = scaled_data.iloc[:, 1] * scale_factor
        
        # 保存缩放后的数据
        scaled_data.to_csv('v1_scaled.txt', sep='\t', index=False, encoding='utf-8')
        
        print(f"✅ 缩放后的训练数据已保存到: v1_scaled.txt")
        print(f"   缩放后范围: {scaled_data.iloc[:, 1].min():.6f} ~ {scaled_data.iloc[:, 1].max():.6f} μm")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建缩放修复失败: {e}")
        return False

def main():
    """主函数"""
    print("数据异常诊断程序")
    
    # 分析数据问题
    analyze_data_issue()
    
    # 创建临时修复方案
    create_data_scaling_fix()
    
    print("\n" + "="*70)
    print("🎯 诊断总结")
    print("="*70)
    print("问题: 数据范围从正常的几微米变成了约22000微米")
    print("影响: 导致预测误差达到21993.67μm，完全无法使用")
    print("原因: 很可能是AFM设备状态、传感器校准或配置问题")
    print("建议: 优先检查设备状态和传感器校准")
    print("临时方案: 使用v1_scaled.txt作为训练数据进行测试")
    print("="*70)

if __name__ == "__main__":
    main()

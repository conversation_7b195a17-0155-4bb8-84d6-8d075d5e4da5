#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析v1.txt在代码运行过程中的两次变化
"""

import re

def analyze_v1_changes_from_log():
    """从运行日志中分析v1.txt的两次变化"""
    print("="*70)
    print("🔍 分析v1.txt在代码运行过程中的两次变化")
    print("="*70)
    
    # 从之前的运行日志中提取关键信息
    log_analysis = """
    根据运行日志，v1.txt确实发生了两次变化：
    
    第一次变化：
    💾 保存训练数据到 v1.txt...
    ✅ 训练数据已保存: 5000 个位移数据点
       文件: v1.txt
       数据范围: -89.836937 ~ -89.638260 μm
    
    第二次变化：
    💾 保存训练数据到 v1.txt...
    ✅ 训练数据已保存: 5000 个位移数据点
       文件: v1.txt
       数据范围: 2.492051 ~ 2.846040 μm
    """
    
    print("1. 从运行日志识别的两次变化:")
    print(log_analysis)
    
    return True

def explain_double_change():
    """解释为什么会发生两次变化"""
    print("\n2. 两次变化的原因分析:")
    
    changes = [
        {
            "变化": "第一次变化",
            "时机": "程序启动初期",
            "数据范围": "-89.84 ~ -89.64 μm",
            "原因": [
                "程序启动时首先收集训练数据",
                "数据收集器开始工作，收集到5000个点",
                "数据保存到v1.txt，覆盖之前的文件"
            ],
            "特点": "数据范围异常大（约-90μm），可能是设备初始化状态"
        },
        {
            "变化": "第二次变化", 
            "时机": "程序运行中期",
            "数据范围": "2.49 ~ 2.85 μm",
            "原因": [
                "程序继续运行，可能重新启动了数据收集",
                "设备状态稳定后，产生了新的数据",
                "新数据再次覆盖v1.txt文件"
            ],
            "特点": "数据范围恢复正常（约2-3μm），设备状态稳定"
        }
    ]
    
    for i, change in enumerate(changes, 1):
        print(f"\n   {change['变化']}:")
        print(f"   时机: {change['时机']}")
        print(f"   数据范围: {change['数据范围']}")
        print(f"   特点: {change['特点']}")
        print(f"   原因:")
        for reason in change['原因']:
            print(f"      • {reason}")

def analyze_code_logic():
    """分析代码逻辑中可能导致两次变化的原因"""
    print("\n3. 代码逻辑分析:")
    
    print("   🔍 可能的代码执行路径:")
    
    scenarios = [
        {
            "场景": "双重数据收集器启动",
            "描述": "程序可能启动了两个数据收集器实例",
            "证据": [
                "日志显示了两次'启动位移数据实时可视化'",
                "两次'DSA数据收集器已启动'消息",
                "两次保存训练数据的操作"
            ]
        },
        {
            "场景": "程序重启或重新初始化",
            "描述": "程序在运行过程中可能进行了重新初始化",
            "证据": [
                "两次不同的数据范围表明设备状态发生了变化",
                "第一次数据异常，第二次数据正常",
                "可能是程序检测到异常后重新启动"
            ]
        },
        {
            "场景": "并行数据收集",
            "描述": "可能存在并行的数据收集过程",
            "证据": [
                "位移和速度数据收集可能是并行的",
                "不同的数据源可能产生不同的v1.txt",
                "时间重叠的数据收集操作"
            ]
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n   场景{i}: {scenario['场景']}")
        print(f"   描述: {scenario['描述']}")
        print(f"   证据:")
        for evidence in scenario['证据']:
            print(f"      • {evidence}")

def check_current_v1_state():
    """检查当前v1.txt的状态"""
    print("\n4. 当前v1.txt状态检查:")
    
    try:
        import pandas as pd
        import os
        from datetime import datetime
        
        # 检查文件是否存在
        if not os.path.exists('v1.txt'):
            print("   ❌ v1.txt文件不存在")
            return
        
        # 获取文件信息
        file_stat = os.stat('v1.txt')
        mod_time = datetime.fromtimestamp(file_stat.st_mtime)
        
        # 读取数据
        data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        displacement = data.iloc[:, 1].values
        
        print(f"   📁 文件最后修改: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   📊 当前数据点数: {len(displacement)}")
        print(f"   📊 当前数据范围: {displacement.min():.6f} ~ {displacement.max():.6f} μm")
        print(f"   📊 当前数据均值: {displacement.mean():.6f} μm")
        
        # 判断当前数据属于哪次变化
        if displacement.mean() < -20:
            print("   🔍 当前数据特征: 类似第一次变化（异常大值）")
        elif 2 < displacement.mean() < 5:
            print("   🔍 当前数据特征: 类似第二次变化（正常范围）")
        elif -30 < displacement.mean() < -25:
            print("   🔍 当前数据特征: 新的数据范围（第三次变化？）")
        else:
            print("   🔍 当前数据特征: 未知范围")
            
    except Exception as e:
        print(f"   ❌ 检查失败: {e}")

def provide_prevention_solutions():
    """提供防止多次变化的解决方案"""
    print("\n5. 防止多次变化的解决方案:")
    
    solutions = [
        {
            "方案": "添加文件锁机制",
            "描述": "防止多个进程同时写入v1.txt",
            "实现": [
                "使用文件锁确保只有一个进程能写入",
                "检查文件是否正在被其他进程使用",
                "添加重试机制处理文件冲突"
            ]
        },
        {
            "方案": "添加数据验证",
            "描述": "验证数据质量，异常时不保存",
            "实现": [
                "检查数据范围是否在合理区间内",
                "验证数据点数是否达到要求",
                "异常数据时保留原有v1.txt"
            ]
        },
        {
            "方案": "使用时间戳备份",
            "描述": "每次保存时创建带时间戳的备份",
            "实现": [
                "保存为v1_YYYYMMDD_HHMMSS.txt",
                "保留最近几个版本的备份",
                "用户可以选择使用哪个版本"
            ]
        },
        {
            "方案": "添加用户确认",
            "描述": "数据收集完成后询问是否保存",
            "实现": [
                "显示数据统计信息供用户确认",
                "提供预览功能查看数据质量",
                "用户确认后才覆盖现有文件"
            ]
        }
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"\n   方案{i}: {solution['方案']}")
        print(f"   描述: {solution['描述']}")
        print(f"   实现方法:")
        for impl in solution['实现']:
            print(f"      • {impl}")

def main():
    """主函数"""
    print("v1.txt双重变化分析程序")
    
    # 分析日志中的变化
    analyze_v1_changes_from_log()
    
    # 解释变化原因
    explain_double_change()
    
    # 分析代码逻辑
    analyze_code_logic()
    
    # 检查当前状态
    check_current_v1_state()
    
    # 提供解决方案
    provide_prevention_solutions()
    
    print("\n" + "="*70)
    print("🎯 总结")
    print("="*70)
    print("v1.txt在运行过程中确实发生了两次变化:")
    print("1. 🔄 第一次: -89.84 ~ -89.64 μm (异常数据)")
    print("2. 🔄 第二次: 2.49 ~ 2.85 μm (正常数据)")
    print("3. 🔄 可能的第三次: -28.73 μm (当前数据)")
    print("\n原因可能是:")
    print("• 多个数据收集器实例同时运行")
    print("• 程序检测到异常数据后重新收集")
    print("• 设备状态在运行过程中发生变化")
    print("\n建议: 添加文件保护机制，防止意外覆盖")

if __name__ == "__main__":
    main()

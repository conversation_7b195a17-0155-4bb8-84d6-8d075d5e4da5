#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析v1.txt数据变化的原因
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def analyze_v1_data_change():
    """分析v1.txt数据变化的原因"""
    print("="*70)
    print("🔍 分析v1.txt数据变化的原因")
    print("="*70)
    
    # 1. 检查当前v1.txt文件
    print("\n1. 当前v1.txt文件分析:")
    try:
        # 获取文件信息
        file_stat = os.stat('v1.txt')
        file_size = file_stat.st_size
        mod_time = datetime.fromtimestamp(file_stat.st_mtime)
        
        print(f"   📁 文件大小: {file_size} bytes")
        print(f"   🕐 最后修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 读取数据
        data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        displacement = data.iloc[:, 1].values
        
        print(f"   📊 数据点数: {len(displacement)}")
        print(f"   📊 数据范围: {displacement.min():.6f} ~ {displacement.max():.6f} μm")
        print(f"   📊 数据均值: {displacement.mean():.6f} μm")
        print(f"   📊 数据标准差: {displacement.std():.6f} μm")
        print(f"   📊 数据变化范围: {displacement.max() - displacement.min():.6f} μm")
        
        # 显示前5个和后5个数据点
        print(f"   📝 前5个数据点: {displacement[:5]}")
        print(f"   📝 后5个数据点: {displacement[-5:]}")
        
        return displacement
        
    except Exception as e:
        print(f"   ❌ 读取v1.txt失败: {e}")
        return None

def explain_data_change():
    """解释数据变化的原因"""
    print("\n2. 数据变化原因分析:")
    
    print("   🔄 v1.txt文件被程序重新生成的原因:")
    
    reasons = [
        {
            "原因": "程序设计逻辑",
            "说明": "integrated_real_time_demo.py 每次运行都会重新收集5000个训练数据点",
            "详细": [
                "程序启动时首先收集训练数据",
                "收集到5000个位移数据点后保存到v1.txt",
                "这会覆盖之前的v1.txt文件"
            ]
        },
        {
            "原因": "数据收集器配置",
            "说明": "data_collector_3s.py 中 training_target_count = 5000",
            "详细": [
                "数据收集器被配置为收集5000个训练点",
                "每次启动都会重新收集新的数据",
                "新数据会替换旧的训练数据"
            ]
        },
        {
            "原因": "实时数据特性",
            "说明": "AFM设备每次运行产生的数据都不同",
            "详细": [
                "AFM设备的位移数据是实时变化的",
                "不同时间段的数据范围可能不同",
                "环境因素、设备状态都会影响数据"
            ]
        }
    ]
    
    for i, reason in enumerate(reasons, 1):
        print(f"\n   {i}. {reason['原因']}: {reason['说明']}")
        for detail in reason['详细']:
            print(f"      • {detail}")

def compare_data_ranges():
    """对比不同时期的数据范围"""
    print("\n3. 历史数据范围对比:")
    
    historical_ranges = [
        {
            "时期": "第一次运行（500个点）",
            "范围": "约 2.63-2.80 μm",
            "特点": "小数据集，范围较小"
        },
        {
            "时期": "第二次运行（2000个点）", 
            "范围": "约 -4.39 到 -4.32 μm",
            "特点": "负值范围，数据稳定"
        },
        {
            "时期": "异常数据期",
            "范围": "约 -22000 μm",
            "特点": "异常大值，设备问题"
        },
        {
            "时期": "修复后（5000个点）",
            "范围": "约 -1.31 ~ -1.29 μm",
            "特点": "正常范围，数据量大"
        },
        {
            "时期": "当前运行（5000个点）",
            "范围": "约 -28.73 ~ -28.68 μm",
            "特点": "新的数据范围，设备状态变化"
        }
    ]
    
    for i, period in enumerate(historical_ranges, 1):
        print(f"   {i}. {period['时期']}")
        print(f"      范围: {period['范围']}")
        print(f"      特点: {period['特点']}")

def analyze_current_vs_test_data():
    """分析当前训练数据与测试数据的关系"""
    print("\n4. 训练数据 vs 测试数据分析:")
    
    try:
        # 读取训练数据
        train_data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        train_displacement = train_data.iloc[:, 1].values
        
        # 读取测试数据
        test_data = pd.read_csv('three_seconds_data.txt', sep='\t', encoding='utf-8')
        test_displacement = test_data['位移[μm]'].values
        test_prediction = test_data['预测位移[μm]'].values
        
        print(f"   📊 训练数据范围: {train_displacement.min():.6f} ~ {train_displacement.max():.6f} μm")
        print(f"   📊 测试数据范围: {test_displacement.min():.6f} ~ {test_displacement.max():.6f} μm")
        print(f"   📊 预测数据范围: {test_prediction.min():.6f} ~ {test_prediction.max():.6f} μm")
        
        # 检查数据范围匹配度
        train_mean = train_displacement.mean()
        test_mean = test_displacement.mean()
        pred_mean = test_prediction.mean()
        
        print(f"\n   📈 数据均值对比:")
        print(f"      训练数据均值: {train_mean:.6f} μm")
        print(f"      测试数据均值: {test_mean:.6f} μm")
        print(f"      预测数据均值: {pred_mean:.6f} μm")
        
        # 分析匹配度
        train_test_diff = abs(train_mean - test_mean)
        train_pred_diff = abs(train_mean - pred_mean)
        
        print(f"\n   🔍 匹配度分析:")
        print(f"      训练-测试差异: {train_test_diff:.6f} μm")
        print(f"      训练-预测差异: {train_pred_diff:.6f} μm")
        
        if train_test_diff < 1.0:
            print("      ✅ 训练和测试数据范围匹配良好")
        else:
            print("      ⚠️  训练和测试数据范围差异较大")
            
        if train_pred_diff > 10.0:
            print("      ❌ 预测结果与训练数据范围严重不匹配")
        else:
            print("      ✅ 预测结果与训练数据范围匹配")
            
    except Exception as e:
        print(f"   ❌ 数据对比分析失败: {e}")

def provide_solutions():
    """提供解决方案"""
    print("\n5. 解决方案建议:")
    
    solutions = [
        {
            "问题": "v1.txt数据频繁变化",
            "解决方案": [
                "如果需要固定训练数据，可以备份v1.txt文件",
                "修改程序逻辑，检查v1.txt是否存在且满足要求时跳过重新收集",
                "添加用户确认机制，询问是否重新收集训练数据"
            ]
        },
        {
            "问题": "预测结果与训练数据不匹配",
            "解决方案": [
                "检查模型是否真的使用了新的v1.txt数据进行训练",
                "验证标准化器是否正确拟合了新的数据范围",
                "重新训练模型，确保使用当前的v1.txt数据"
            ]
        },
        {
            "问题": "数据范围变化太大",
            "解决方案": [
                "检查AFM设备状态，确保设备工作正常",
                "记录每次运行的设备参数和环境条件",
                "建立数据范围监控机制，异常时报警"
            ]
        }
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"\n   {i}. 问题: {solution['问题']}")
        for j, sol in enumerate(solution['解决方案'], 1):
            print(f"      解决方案{j}: {sol}")

def main():
    """主函数"""
    print("v1.txt数据变化分析程序")
    
    # 分析当前数据
    current_data = analyze_v1_data_change()
    
    # 解释变化原因
    explain_data_change()
    
    # 对比历史数据
    compare_data_ranges()
    
    # 分析训练vs测试数据
    analyze_current_vs_test_data()
    
    # 提供解决方案
    provide_solutions()
    
    print("\n" + "="*70)
    print("🎯 总结")
    print("="*70)
    print("v1.txt数据变化的主要原因:")
    print("1. 🔄 程序每次运行都重新收集5000个训练数据点")
    print("2. 📊 AFM设备的实时数据本身就在变化")
    print("3. 🔧 当前数据范围约-28.7μm，与之前的数据完全不同")
    print("4. ❌ 预测模型可能没有使用新数据正确训练")
    print("\n建议: 检查模型训练过程，确保使用了当前v1.txt数据")

if __name__ == "__main__":
    main()

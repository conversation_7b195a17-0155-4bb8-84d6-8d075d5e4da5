#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析预测位移偏差大的原因
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

def analyze_prediction_bias():
    """分析预测偏差的原因"""
    print("="*70)
    print("🔍 预测位移偏差大的原因分析")
    print("="*70)
    
    # 1. 加载当前测试数据
    print("\n1. 当前测试数据分析:")
    test_data = pd.read_csv('three_seconds_data.txt', sep='\t', encoding='utf-8')
    
    real_displacement = test_data['位移[μm]'].values
    predicted_displacement = test_data['预测位移[μm]'].values
    
    print(f"   📊 真实位移范围: {real_displacement.min():.6f} ~ {real_displacement.max():.6f} μm")
    print(f"   📊 预测位移范围: {predicted_displacement.min():.6f} ~ {predicted_displacement.max():.6f} μm")
    print(f"   📊 真实位移均值: {real_displacement.mean():.6f} μm")
    print(f"   📊 预测位移均值: {predicted_displacement.mean():.6f} μm")
    print(f"   📊 真实位移变化范围: {real_displacement.max() - real_displacement.min():.6f} μm")
    print(f"   📊 预测位移变化范围: {predicted_displacement.max() - predicted_displacement.min():.6f} μm")
    
    # 计算偏差
    bias = predicted_displacement.mean() - real_displacement.mean()
    mae = np.mean(np.abs(predicted_displacement - real_displacement))
    
    print(f"   ❌ 系统性偏差: {bias:.6f} μm")
    print(f"   ❌ 平均绝对误差: {mae:.6f} μm")
    
    # 2. 检查训练数据
    print("\n2. 训练数据分析:")
    try:
        train_data = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        train_displacement = train_data.iloc[:, 1].values
        
        print(f"   📊 训练数据范围: {train_displacement.min():.6f} ~ {train_displacement.max():.6f} μm")
        print(f"   📊 训练数据均值: {train_displacement.mean():.6f} μm")
        print(f"   📊 训练数据变化范围: {train_displacement.max() - train_displacement.min():.6f} μm")
        
        # 计算训练和测试数据的差异
        train_mean = train_displacement.mean()
        test_mean = real_displacement.mean()
        range_diff = abs((train_displacement.max() - train_displacement.min()) - 
                        (real_displacement.max() - real_displacement.min()))
        
        print(f"   🔍 训练vs测试均值差异: {abs(train_mean - test_mean):.6f} μm")
        print(f"   🔍 训练vs测试范围差异: {range_diff:.6f} μm")
        
    except Exception as e:
        print(f"   ❌ 无法读取训练数据: {e}")
    
    # 3. 检查是否使用了旧模型
    print("\n3. 模型来源分析:")
    
    # 检查预测值的特征
    pred_std = predicted_displacement.std()
    pred_range = predicted_displacement.max() - predicted_displacement.min()
    
    print(f"   📊 预测值标准差: {pred_std:.8f} μm")
    print(f"   📊 预测值变化范围: {pred_range:.8f} μm")
    
    if pred_std < 0.001:  # 预测值变化很小
        print("   ⚠️  预测值几乎不变，可能原因:")
        print("      - 使用了错误的模型或标准化器")
        print("      - 模型训练数据与当前数据范围不匹配")
        print("      - 模型过度拟合到特定数值")
    
    # 4. 分析可能的原因
    print("\n4. 偏差原因分析:")
    
    reasons = []
    
    # 检查数据范围匹配
    if abs(train_mean - test_mean) > 0.1:
        reasons.append("训练数据和测试数据的均值差异过大")
    
    if abs(bias) > 1.0:
        reasons.append("存在严重的系统性偏差")
    
    if pred_std < 0.001:
        reasons.append("预测值缺乏变化，模型可能有问题")
    
    # 检查预测值是否在合理范围内
    if abs(predicted_displacement.mean()) > abs(real_displacement.mean()) * 2:
        reasons.append("预测值的数量级与真实值不匹配")
    
    for i, reason in enumerate(reasons, 1):
        print(f"   {i}. {reason}")
    
    # 5. 具体数值对比
    print("\n5. 具体数值对比:")
    print(f"   真实位移: {real_displacement.mean():.6f} μm (变化范围: ±{(real_displacement.max()-real_displacement.min())/2:.6f} μm)")
    print(f"   预测位移: {predicted_displacement.mean():.6f} μm (变化范围: ±{(predicted_displacement.max()-predicted_displacement.min())/2:.8f} μm)")
    print(f"   偏差倍数: {abs(predicted_displacement.mean() / real_displacement.mean()):.1f}倍")
    
    return real_displacement, predicted_displacement, train_displacement

def plot_bias_analysis(real_disp, pred_disp, train_disp):
    """绘制偏差分析图"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 数据范围对比
    categories = ['训练数据', '真实位移', '预测位移']
    means = [train_disp.mean(), real_disp.mean(), pred_disp.mean()]
    ranges = [train_disp.max()-train_disp.min(), 
              real_disp.max()-real_disp.min(), 
              pred_disp.max()-pred_disp.min()]
    
    x = np.arange(len(categories))
    width = 0.35
    
    axes[0, 0].bar(x - width/2, means, width, label='均值', alpha=0.8)
    axes[0, 0].bar(x + width/2, ranges, width, label='变化范围', alpha=0.8)
    axes[0, 0].set_xlabel('数据类型')
    axes[0, 0].set_ylabel('位移 [μm]')
    axes[0, 0].set_title('数据范围对比')
    axes[0, 0].set_xticks(x)
    axes[0, 0].set_xticklabels(categories)
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 时间序列对比
    time_axis = np.arange(len(real_disp)) * 0.015
    axes[0, 1].plot(time_axis, real_disp, 'b-', label='真实位移', linewidth=2)
    axes[0, 1].plot(time_axis, pred_disp, 'r--', label='预测位移', linewidth=2)
    axes[0, 1].set_xlabel('时间 [s]')
    axes[0, 1].set_ylabel('位移 [μm]')
    axes[0, 1].set_title('真实vs预测位移时间序列')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 误差分析
    error = pred_disp - real_disp
    axes[1, 0].plot(time_axis, error, 'g-', linewidth=2)
    axes[1, 0].axhline(y=0, color='k', linestyle='--', alpha=0.5)
    axes[1, 0].axhline(y=error.mean(), color='r', linestyle='--', 
                       label=f'平均误差: {error.mean():.3f} μm')
    axes[1, 0].set_xlabel('时间 [s]')
    axes[1, 0].set_ylabel('预测误差 [μm]')
    axes[1, 0].set_title('预测误差时间序列')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 数据分布对比
    axes[1, 1].hist(train_disp, bins=20, alpha=0.5, label='训练数据', density=True)
    axes[1, 1].hist(real_disp, bins=20, alpha=0.5, label='真实位移', density=True)
    axes[1, 1].axvline(pred_disp.mean(), color='red', linestyle='--', 
                       linewidth=3, label=f'预测均值: {pred_disp.mean():.3f}')
    axes[1, 1].set_xlabel('位移 [μm]')
    axes[1, 1].set_ylabel('密度')
    axes[1, 1].set_title('数据分布对比')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('prediction_bias_analysis.png', dpi=300, bbox_inches='tight')
    print("偏差分析图已保存为 prediction_bias_analysis.png")
    plt.close()

def provide_solutions():
    """提供解决方案"""
    print("\n" + "="*70)
    print("💡 解决方案建议")
    print("="*70)
    
    solutions = [
        {
            "问题": "训练数据与测试数据范围不匹配",
            "解决方案": [
                "重新收集与当前测试环境一致的训练数据",
                "使用当前的真实位移数据重新训练模型",
                "增加数据增强技术，扩大训练数据的覆盖范围"
            ]
        },
        {
            "问题": "模型使用了错误的标准化器",
            "解决方案": [
                "检查标准化器的拟合范围是否正确",
                "重新拟合标准化器到当前数据范围",
                "使用robust scaling代替min-max scaling"
            ]
        },
        {
            "问题": "系统性偏差过大",
            "解决方案": [
                "添加偏差校正机制：prediction_corrected = prediction - bias",
                "使用在线学习更新模型参数",
                "重新设计模型架构，提高泛化能力"
            ]
        },
        {
            "问题": "预测值缺乏变化",
            "解决方案": [
                "检查模型是否过度拟合",
                "增加模型复杂度或调整超参数",
                "使用更多样化的训练数据"
            ]
        }
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"\n{i}. 问题: {solution['问题']}")
        for j, sol in enumerate(solution['解决方案'], 1):
            print(f"   解决方案{j}: {sol}")

def main():
    """主函数"""
    print("预测位移偏差分析程序")
    
    # 分析偏差原因
    real_disp, pred_disp, train_disp = analyze_prediction_bias()
    
    # 绘制分析图
    plot_bias_analysis(real_disp, pred_disp, train_disp)
    
    # 提供解决方案
    provide_solutions()
    
    print(f"\n" + "="*70)
    print("🎯 总结")
    print("="*70)
    print("预测位移偏差大的主要原因:")
    print("1. 🔴 训练数据范围 (-1.19 ~ -1.14 μm) 与测试数据范围 (-1.47 ~ -1.46 μm) 不匹配")
    print("2. 🔴 预测值几乎不变 (约-5.016 μm)，说明模型可能使用了错误的参数")
    print("3. 🔴 存在3.5倍的系统性偏差")
    print("4. 🔴 可能使用了之前训练的模型，而不是当前数据训练的模型")
    print("\n建议立即重新训练模型或应用偏差校正！")

if __name__ == "__main__":
    main()

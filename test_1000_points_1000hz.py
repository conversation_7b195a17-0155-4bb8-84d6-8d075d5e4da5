#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试1000个数据点、1000Hz采样率的配置
"""

import os
import asyncio
import pandas as pd
import numpy as np
import time

async def test_1000_points_config():
    """测试1000个数据点、1000Hz采样率的配置"""
    print("="*70)
    print("🧪 测试1000个数据点、1000Hz采样率配置")
    print("="*70)
    
    # 1. 清理旧文件
    print("\n1. 清理旧文件...")
    files_to_remove = [
        'v1.txt',
        'displacement_model_fixed.pth',
        'displacement_scaler_fixed.pkl',
        'training_info_fixed.pkl',
        'three_seconds_data.txt'
    ]
    
    for file in files_to_remove:
        if os.path.exists(file):
            os.remove(file)
            print(f"   🗑️  删除: {file}")
    
    # 2. 导入必要的模块
    print("\n2. 导入模块...")
    try:
        from real_time_data_bridge import RealTimeVisualizationBridge
        from real_time_train import train_displacement_model
        print("   ✅ 模块导入成功")
    except Exception as e:
        print(f"   ❌ 模块导入失败: {e}")
        return False
    
    # 3. 检查DLL文件
    print("\n3. 检查DLL文件...")
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"   ❌ 找不到DLL文件: {dll_path}")
        return False
    print(f"   ✅ DLL文件存在: {dll_path}")
    
    # 4. 收集1000个训练数据点
    print("\n4. 收集1000个训练数据点...")
    bridge = RealTimeVisualizationBridge(dll_path)
    
    # 启动数据收集
    if not await bridge.start_displacement_visualization():
        print("   ❌ 启动DSA数据收集失败")
        return False
    
    displacement_source = bridge.get_displacement_source()
    
    # 等待训练数据收集完成
    print("   等待收集1000个位移数据点...")
    training_collected = False
    check_count = 0
    start_time = time.time()
    
    while not training_collected and check_count < 30:  # 最多等待30秒
        await asyncio.sleep(0.5)  # 更频繁检查
        check_count += 1
        
        # 检查训练数据收集状态
        if hasattr(displacement_source.collector, 'get_training_data_status'):
            status = displacement_source.collector.get_training_data_status()
            elapsed = time.time() - start_time
            rate = status['count'] / elapsed if elapsed > 0 else 0
            print(f"   进度: {status['count']}/{status['target']} ({status['progress']:.1f}%) - 速率: {rate:.1f} 点/秒")
            training_collected = status['collected']
    
    if not training_collected:
        print("   ❌ 训练数据收集超时")
        bridge.stop_all()
        return False
    
    print("   ✅ 训练数据收集完成！")
    
    # 停止数据收集
    bridge.stop_all()
    await asyncio.sleep(2.0)
    
    # 5. 验证v1.txt文件
    print("\n5. 验证v1.txt文件...")
    if not os.path.exists('v1.txt'):
        print("   ❌ v1.txt文件不存在")
        return False
    
    try:
        train_df = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        time_data = train_df.iloc[:, 0].values
        displacement_data = train_df.iloc[:, 1].values
        
        # 时间分析
        duration_ms = time_data[-1] - time_data[0]
        duration_s = duration_ms / 1000
        avg_interval = np.mean(np.diff(time_data))
        actual_frequency = 1000 / avg_interval
        
        print(f"   📊 数据点数: {len(displacement_data)}")
        print(f"   ⏰ 时间跨度: {duration_ms:.3f} ms ({duration_s:.3f} 秒)")
        print(f"   📈 平均采样间隔: {avg_interval:.3f} ms")
        print(f"   📈 实际采样频率: {actual_frequency:.1f} Hz")
        print(f"   📊 位移范围: {displacement_data.min():.6f} ~ {displacement_data.max():.6f} μm")
        print(f"   📊 位移均值: {displacement_data.mean():.6f} μm")
        
        # 验证目标
        target_points = 1000
        target_frequency = 1000
        target_duration = 1.0  # 1秒
        
        print(f"\n   🎯 目标验证:")
        print(f"      目标数据点数: {target_points}, 实际: {len(displacement_data)}")
        if len(displacement_data) >= target_points:
            print(f"      ✅ 数据点数达标")
        else:
            print(f"      ⚠️  数据点数不足")
        
        print(f"      目标采样频率: {target_frequency} Hz, 实际: {actual_frequency:.1f} Hz")
        if abs(actual_frequency - target_frequency) < 100:  # 允许100Hz误差
            print(f"      ✅ 采样频率接近目标")
        else:
            print(f"      ⚠️  采样频率偏差较大")
        
        print(f"      目标时长: {target_duration} 秒, 实际: {duration_s:.3f} 秒")
        if abs(duration_s - target_duration) < 0.2:  # 允许0.2秒误差
            print(f"      ✅ 时长接近目标")
        else:
            print(f"      ⚠️  时长偏差较大")
            
    except Exception as e:
        print(f"   ❌ 读取v1.txt失败: {e}")
        return False
    
    # 6. 训练模型
    print("\n6. 使用新数据训练模型...")
    try:
        model, scaler = train_displacement_model(
            data_file='v1.txt',
            window_size=20,  # 适应1000个数据点的窗口大小
            hidden_size=64,  # 适中的隐藏层大小
            learning_rate=0.01,
            epochs=80,  # 减少训练轮数以加快测试
            train_points=len(displacement_data)
        )
        
        if model is None:
            print("   ❌ 模型训练失败")
            return False
        
        print("   ✅ 模型训练成功")
        
        # 检查标准化器
        if scaler is not None:
            scaler_min, scaler_max = scaler.data_min_[0], scaler.data_max_[0]
            print(f"   📊 标准化器范围: {scaler_min:.6f} ~ {scaler_max:.6f} μm")
            
            # 验证一致性
            if abs(scaler_min - displacement_data.min()) < 1e-6 and abs(scaler_max - displacement_data.max()) < 1e-6:
                print("   ✅ 标准化器与训练数据范围一致")
            else:
                print("   ⚠️  标准化器与训练数据范围不一致")
        
    except Exception as e:
        print(f"   ❌ 模型训练失败: {e}")
        return False
    
    # 7. 验证模型文件
    print("\n7. 验证模型文件...")
    model_files = [
        'displacement_model_fixed.pth',
        'displacement_scaler_fixed.pkl',
        'training_info_fixed.pkl'
    ]
    
    all_files_exist = True
    for file in model_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   ✅ {file} (大小: {size} bytes)")
        else:
            print(f"   ❌ {file} (不存在)")
            all_files_exist = False
    
    return all_files_exist

def compare_configurations():
    """对比不同配置的特点"""
    print("\n8. 配置对比分析:")
    
    configs = [
        {
            "配置": "原始配置",
            "数据点数": 5000,
            "采样频率": "4000 Hz",
            "时长": "1.25 秒",
            "特点": "高密度，训练时间长，数据丰富"
        },
        {
            "配置": "新配置",
            "数据点数": 1000,
            "采样频率": "1000 Hz", 
            "时长": "1.0 秒",
            "特点": "适中密度，训练快速，数据精简"
        }
    ]
    
    for i, config in enumerate(configs, 1):
        print(f"\n   配置{i}: {config['配置']}")
        print(f"      数据点数: {config['数据点数']}")
        print(f"      采样频率: {config['采样频率']}")
        print(f"      时长: {config['时长']}")
        print(f"      特点: {config['特点']}")
    
    print(f"\n   🎯 新配置的优势:")
    print(f"      • 数据收集时间缩短80% (1.25秒 → 1.0秒)")
    print(f"      • 模型训练速度提升 (数据量减少80%)")
    print(f"      • 采样频率仍然足够高 (1000Hz)")
    print(f"      • 保持足够的时间序列信息")

async def main():
    """主函数"""
    print("1000个数据点、1000Hz采样率配置测试")
    
    success = await test_1000_points_config()
    
    # 配置对比
    compare_configurations()
    
    print("\n" + "="*70)
    if success:
        print("🎉 测试成功！")
        print("✅ 1000个数据点、1000Hz采样率配置正常")
        print("✅ 数据收集时间约1秒")
        print("✅ 模型训练成功")
        print("✅ 配置优化达到预期效果")
        print("\n现在可以运行完整的演示程序:")
        print("  python integrated_real_time_demo.py")
    else:
        print("❌ 测试失败！")
        print("请检查:")
        print("  1. DSA设备连接")
        print("  2. 数据收集器配置")
        print("  3. 模型训练参数")
    print("="*70)

if __name__ == "__main__":
    asyncio.run(main())

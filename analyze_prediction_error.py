#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深入分析预测误差的原因
"""

import pandas as pd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

def load_and_analyze_data(filename):
    """
    加载并分析数据
    """
    data = pd.read_csv(filename, sep='\t', encoding='utf-8')
    data = data.dropna()
    
    print("=== 数据基本信息 ===")
    print(f"数据点总数: {len(data)}")
    print(f"时间范围: {data['时间[s]'].min():.3f} - {data['时间[s]'].max():.3f} 秒")
    print(f"真实位移范围: {data['位移[μm]'].min():.6f} - {data['位移[μm]'].max():.6f} μm")
    print(f"预测位移范围: {data['预测位移[μm]'].min():.6f} - {data['预测位移[μm]'].max():.6f} μm")
    
    # 计算误差
    data['误差'] = data['位移[μm]'] - data['预测位移[μm]']
    data['绝对误差'] = np.abs(data['误差'])
    data['相对误差(%)'] = (data['误差'] / data['位移[μm]']) * 100
    
    print(f"\n=== 误差统计 ===")
    print(f"平均误差: {data['误差'].mean():.6f} μm")
    print(f"误差标准差: {data['误差'].std():.6f} μm")
    print(f"最大正误差: {data['误差'].max():.6f} μm")
    print(f"最大负误差: {data['误差'].min():.6f} μm")
    print(f"平均绝对误差: {data['绝对误差'].mean():.6f} μm")
    print(f"平均相对误差: {data['相对误差(%)'].mean():.3f}%")
    
    return data

def analyze_error_patterns(data):
    """
    分析误差模式
    """
    print("\n=== 误差模式分析 ===")
    
    # 1. 按时间段分析
    print("\n1. 按秒数分析:")
    for second in sorted(data['数据秒数'].unique()):
        if pd.notna(second):
            subset = data[data['数据秒数'] == second]
            print(f"  {second}:")
            print(f"    数据点数: {len(subset)}")
            print(f"    平均误差: {subset['误差'].mean():.6f} μm")
            print(f"    误差标准差: {subset['误差'].std():.6f} μm")
            print(f"    最大绝对误差: {subset['绝对误差'].max():.6f} μm")
            print(f"    平均相对误差: {subset['相对误差(%)'].mean():.3f}%")
    
    # 2. 分析误差趋势
    print("\n2. 误差趋势分析:")
    # 计算滑动平均误差
    window_size = 10
    data['误差_滑动平均'] = data['误差'].rolling(window=window_size, center=True).mean()
    
    # 检查是否存在系统性偏差
    positive_errors = (data['误差'] > 0).sum()
    negative_errors = (data['误差'] < 0).sum()
    print(f"    正误差数量: {positive_errors} ({positive_errors/len(data)*100:.1f}%)")
    print(f"    负误差数量: {negative_errors} ({negative_errors/len(data)*100:.1f}%)")
    
    # 3. 分析误差与其他变量的关系
    print("\n3. 误差与其他变量的关系:")
    
    # 与速度的关系
    speed_corr = np.corrcoef(data['速度[μm/s]'], data['绝对误差'])[0, 1]
    print(f"    绝对误差与速度的相关系数: {speed_corr:.4f}")
    
    # 与处理时间的关系
    if '处理时间[ms]' in data.columns:
        # 排除处理时间为0的数据点
        valid_time_data = data[data['处理时间[ms]'] > 0]
        if len(valid_time_data) > 0:
            time_corr = np.corrcoef(valid_time_data['处理时间[ms]'], valid_time_data['绝对误差'])[0, 1]
            print(f"    绝对误差与处理时间的相关系数: {time_corr:.4f}")
    
    return data

def plot_detailed_error_analysis(data):
    """
    绘制详细的误差分析图
    """
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 误差时间序列
    axes[0, 0].plot(data['时间[s]'], data['误差'], 'b-', alpha=0.7, linewidth=1)
    axes[0, 0].axhline(y=0, color='r', linestyle='--', alpha=0.5)
    axes[0, 0].set_xlabel('时间 [s]')
    axes[0, 0].set_ylabel('预测误差 [μm]')
    axes[0, 0].set_title('预测误差随时间变化')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 添加秒数分界线
    for second in [1.0, 2.0]:
        axes[0, 0].axvline(x=second, color='gray', linestyle=':', alpha=0.5)
    
    # 2. 绝对误差时间序列
    axes[0, 1].plot(data['时间[s]'], data['绝对误差'], 'r-', alpha=0.7, linewidth=1)
    axes[0, 1].set_xlabel('时间[s]')
    axes[0, 1].set_ylabel('绝对误差 [μm]')
    axes[0, 1].set_title('绝对误差随时间变化')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 误差分布直方图
    axes[0, 2].hist(data['误差'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 2].axvline(x=data['误差'].mean(), color='r', linestyle='--', 
                       label=f'平均值: {data["误差"].mean():.6f}')
    axes[0, 2].set_xlabel('预测误差 [μm]')
    axes[0, 2].set_ylabel('频次')
    axes[0, 2].set_title('误差分布直方图')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. 误差 vs 速度
    axes[1, 0].scatter(data['速度[μm/s]'], data['绝对误差'], alpha=0.6, s=20)
    axes[1, 0].set_xlabel('速度 [μm/s]')
    axes[1, 0].set_ylabel('绝对误差 [μm]')
    axes[1, 0].set_title('绝对误差 vs 速度')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. 误差 vs 真实位移
    axes[1, 1].scatter(data['位移[μm]'], data['误差'], alpha=0.6, s=20)
    axes[1, 1].axhline(y=0, color='r', linestyle='--', alpha=0.5)
    axes[1, 1].set_xlabel('真实位移 [μm]')
    axes[1, 1].set_ylabel('预测误差 [μm]')
    axes[1, 1].set_title('预测误差 vs 真实位移')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 6. 相对误差分布
    axes[1, 2].hist(data['相对误差(%)'], bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[1, 2].axvline(x=data['相对误差(%)'].mean(), color='r', linestyle='--',
                       label=f'平均值: {data["相对误差(%)"].mean():.3f}%')
    axes[1, 2].set_xlabel('相对误差 [%]')
    axes[1, 2].set_ylabel('频次')
    axes[1, 2].set_title('相对误差分布')
    axes[1, 2].legend()
    axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('detailed_error_analysis.png', dpi=300, bbox_inches='tight')
    print("详细误差分析图已保存为 detailed_error_analysis.png")
    plt.close()

def identify_error_causes(data):
    """
    识别误差的可能原因
    """
    print("\n=== 误差原因分析 ===")
    
    # 1. 检查数据特征
    displacement_range = data['位移[μm]'].max() - data['位移[μm]'].min()
    avg_error = data['绝对误差'].mean()
    error_to_range_ratio = avg_error / displacement_range * 100
    
    print(f"1. 数据特征:")
    print(f"   位移变化范围: {displacement_range:.6f} μm")
    print(f"   平均绝对误差: {avg_error:.6f} μm")
    print(f"   误差占变化范围比例: {error_to_range_ratio:.2f}%")
    
    # 2. 检查预测模型的系统性偏差
    mean_error = data['误差'].mean()
    if abs(mean_error) > data['误差'].std() * 0.1:
        print(f"\n2. 发现系统性偏差:")
        print(f"   平均误差: {mean_error:.6f} μm")
        if mean_error > 0:
            print("   模型倾向于低估真实位移")
        else:
            print("   模型倾向于高估真实位移")
    
    # 3. 检查误差的时间依赖性
    print(f"\n3. 时间依赖性分析:")
    first_half = data[:len(data)//2]
    second_half = data[len(data)//2:]
    
    print(f"   前半段平均绝对误差: {first_half['绝对误差'].mean():.6f} μm")
    print(f"   后半段平均绝对误差: {second_half['绝对误差'].mean():.6f} μm")
    
    # 4. 检查速度对误差的影响
    print(f"\n4. 速度影响分析:")
    low_speed = data[data['速度[μm/s]'] < data['速度[μm/s]'].median()]
    high_speed = data[data['速度[μm/s]'] >= data['速度[μm/s]'].median()]
    
    print(f"   低速时平均绝对误差: {low_speed['绝对误差'].mean():.6f} μm")
    print(f"   高速时平均绝对误差: {high_speed['绝对误差'].mean():.6f} μm")
    
    # 5. 提出改进建议
    print(f"\n=== 改进建议 ===")
    
    if error_to_range_ratio > 10:
        print("1. 误差相对较大，建议:")
        print("   - 检查模型训练数据的质量和数量")
        print("   - 考虑使用更复杂的模型架构")
        print("   - 增加特征工程")
    
    if abs(mean_error) > data['误差'].std() * 0.1:
        print("2. 存在系统性偏差，建议:")
        print("   - 检查训练数据是否存在偏差")
        print("   - 考虑添加偏差校正")
    
    if second_half['绝对误差'].mean() > first_half['绝对误差'].mean() * 1.2:
        print("3. 误差随时间增加，建议:")
        print("   - 检查模型是否存在累积误差")
        print("   - 考虑在线学习或模型更新机制")

def main():
    """
    主函数
    """
    print("开始分析预测误差...")
    
    # 加载和基本分析
    data = load_and_analyze_data('three_seconds_data.txt')
    
    # 误差模式分析
    data = analyze_error_patterns(data)
    
    # 绘制详细分析图
    plot_detailed_error_analysis(data)
    
    # 识别误差原因
    identify_error_causes(data)

if __name__ == "__main__":
    main()
